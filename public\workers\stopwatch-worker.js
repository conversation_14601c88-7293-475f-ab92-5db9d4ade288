// --- Worker State ---
let intervalId = null;
let startTime = 0; // Timestamp (ms) du dernier démarrage/reprise
let accumulatedTime = 0; // Temps total accumulé (en secondes) avant la dernière pause
let currentItemElapsedTime = 0; // Temps écoulé (en secondes) pour l'item d'agenda actuel - PEUT ETRE DECIMAL
let isRunning = false;
let lastTickTimestamp = 0; // Timestamp of the last tick execution for delta calculation
let lastHeartbeat = Date.now(); // For detecting suspension/resumption

// --- Agenda State ---
let agendaItems = []; // Copie locale de la liste des items d'agenda
let currentAgendaIndex = 0; // Index de l'item d'agenda en cours
let plannedDurationSeconds = 0; // Durée totale planifiée (en secondes)
let lastTickDataSent = null; // Pour éviter d'envoyer des messages identiques inutilement
let alertShownForCurrentLimit = false; // Pour ne montrer l'alerte qu'une fois par dépassement

// --- Constants ---
const TICK_INTERVAL_MS = 250; // Fréquence de mise à jour (plus rapide pour fluidité)
const ALERT_THRESHOLD_SECONDS = 300; // Seuil pour l'alerte

// --- Helper Functions ---

function calculateShowAlert(totalSeconds) {
    if (!isRunning || plannedDurationSeconds <= ALERT_THRESHOLD_SECONDS) {
        alertShownForCurrentLimit = false;
        return false;
    }
    const shouldShow = totalSeconds >= plannedDurationSeconds - ALERT_THRESHOLD_SECONDS && totalSeconds < plannedDurationSeconds;

    if (shouldShow && !alertShownForCurrentLimit) {
        alertShownForCurrentLimit = true;
        return true;
    } else if (!shouldShow) {
        alertShownForCurrentLimit = false;
    }
    return shouldShow;
}

function pauseTimer(sendPausedMessage = true) {
    if (intervalId) {
        clearInterval(intervalId);
        intervalId = null;
    }
    // Capture final time before setting isRunning to false
    let finalTotalTime = accumulatedTime;
    if (isRunning && startTime) { // Check isRunning to calc delta only if it was running
        const elapsedSinceStartMs = Date.now() - startTime;
        finalTotalTime = accumulatedTime + Math.floor(elapsedSinceStartMs / 1000); // Use floor for consistency
        accumulatedTime = finalTotalTime; // Update accumulatedTime definitively
    }
    isRunning = false; // Set AFTER calculating final time
    startTime = 0;
    lastTickTimestamp = 0;

    if (sendPausedMessage) {
        // Ensure currentItemElapsedTime is clamped before sending final pause message
        let finalItemTime = currentItemElapsedTime;
        if (agendaItems.length > 0 && currentAgendaIndex < agendaItems.length) {
            const currentItem = agendaItems[currentAgendaIndex];
            if (currentItem) {
                 finalItemTime = Math.min(currentItemElapsedTime, currentItem.duration * 60);
            }
        }

        self.postMessage({
            type: 'paused',
            totalTime: finalTotalTime, // Send the accurately captured final time
            currentItemTime: Math.floor(finalItemTime), // Send floored integer seconds
            currentAgendaIndex: currentAgendaIndex,
            showAlert: calculateShowAlert(finalTotalTime),
        });
        console.log("Worker: Sent final paused state", { totalTime: finalTotalTime, currentItemTime: Math.floor(finalItemTime) });
    }
    lastTickDataSent = null;
    console.log("Worker: Timer paused.");
}

function tick() {
    if (!isRunning) return;

    const now = Date.now();

    // Check for potential suspension/resumption
    const timeSinceLastHeartbeat = now - lastHeartbeat;
    if (timeSinceLastHeartbeat > 2000) {
        console.log('[Stopwatch Worker] Potential suspension detected, recalculating state');
        // Recalculate state after potential suspension
        if (startTime) {
            const totalElapsedMs = now - startTime;
            const totalElapsedSeconds = Math.floor(totalElapsedMs / 1000);
            const newTotalTime = accumulatedTime + totalElapsedSeconds;

            // Send a recovery tick to update the UI
            self.postMessage({
                type: 'tick',
                totalTime: newTotalTime,
                currentItemTime: Math.floor(currentItemElapsedTime),
                currentAgendaIndex: currentAgendaIndex,
                currentItemCompleted: false,
                playSound: null,
                showAlert: calculateShowAlert(newTotalTime),
                resumed: true
            });
        }
    }
    lastHeartbeat = now;

    // Calculate delta time since last tick more accurately
    const deltaMs = lastTickTimestamp ? now - lastTickTimestamp : (now - startTime); // Use delta from start for first tick
    lastTickTimestamp = now; // Update timestamp for the *next* tick

    const deltaSeconds = deltaMs / 1000; // Keep this potentially fractional

    // Update total time using floor for consistency with previous logic, but base on precise start
    const currentTotalRunTimeMs = startTime ? now - startTime : 0;
    const currentTotalTimeSeconds = accumulatedTime + Math.floor(currentTotalRunTimeMs / 1000);

    // *** REVISED: Increment currentItemElapsedTime based on delta ***
    if (agendaItems.length > 0 && currentAgendaIndex < agendaItems.length) {
        currentItemElapsedTime += deltaSeconds; // Increment by calculated delta
        currentItemElapsedTime = Math.max(0, currentItemElapsedTime); // Ensure non-negative
    } else {
        currentItemElapsedTime = 0;
    }

    let newItemCompleted = false;
    let nextAgendaIndex = currentAgendaIndex;
    let shouldPlaySound = null;
    let shouldShowAlert = calculateShowAlert(currentTotalTimeSeconds);
    let timerShouldStop = false;
    let displayItemTime = currentItemElapsedTime; // Time to display/check against duration

    // --- Agenda Logic ---
    if (agendaItems.length > 0 && currentAgendaIndex < agendaItems.length) {
        const currentItem = agendaItems[currentAgendaIndex];
        if (currentItem) {
            const currentItemDurationSeconds = currentItem.duration * 60;
            // Clamp the time used for display and completion check *visuals*
            displayItemTime = Math.min(currentItemElapsedTime, currentItemDurationSeconds);

            // Check for completion using the *unclamped* time
            if (!currentItem.completed && currentItemElapsedTime >= currentItemDurationSeconds) {
                newItemCompleted = true; // Signal completion
                shouldPlaySound = 'bell';
                displayItemTime = currentItemDurationSeconds; // Ensure display shows 100%

                // Send immediate notification request for agenda item completion
                try {
                    self.postMessage({
                        type: 'requestNotification',
                        notificationType: 'agendaItemComplete',
                        itemTitle: currentItem.title,
                        isLastItem: currentAgendaIndex === agendaItems.length - 1,
                        nextItemTitle: agendaItems[currentAgendaIndex + 1]?.title,
                        timestamp: Date.now()
                    });
                } catch (error) {
                    console.warn('[Stopwatch Worker] Failed to send notification request:', error);
                }

                // Also send the legacy notification message for backward compatibility
                try {
                    self.postMessage({
                        type: 'sendNotification',
                        title: `Agenda Item Complete: ${currentItem.title}`,
                        body: currentAgendaIndex === agendaItems.length - 1
                            ? 'Meeting agenda completed!'
                            : `Next: ${agendaItems[currentAgendaIndex + 1]?.title || 'Unknown'}`,
                        tag: 'meeting-agenda-item',
                        requireInteraction: false
                    });
                } catch (error) {
                    console.warn('[Stopwatch Worker] Failed to send legacy notification message:', error);
                }

                // *** REVISED: Check if it's the LAST item ***
                if (currentAgendaIndex === agendaItems.length - 1) {
                    // Yes, last item completed
                    timerShouldStop = true; // Flag to stop AFTER this tick's message
                    console.log("Worker: Last agenda item completed. Stopping timer.");

                    // Send immediate notification request for meeting completion
                    try {
                        self.postMessage({
                            type: 'requestNotification',
                            notificationType: 'meetingComplete',
                            totalTimeSeconds: currentTotalTimeSeconds,
                            totalItems: agendaItems.length,
                            timestamp: Date.now()
                        });
                    } catch (error) {
                        console.warn('[Stopwatch Worker] Failed to send meeting completion notification request:', error);
                    }

                    // Also send legacy notification for backward compatibility
                    try {
                        self.postMessage({
                            type: 'sendNotification',
                            title: 'Meeting Complete!',
                            body: `All agenda items completed in ${Math.floor(currentTotalTimeSeconds / 60)} minutes`,
                            tag: 'meeting-complete',
                            requireInteraction: true
                        });
                    } catch (error) {
                        console.warn('[Stopwatch Worker] Failed to send legacy meeting completion notification:', error);
                    }
                } else {
                    // No, move to next item
                    nextAgendaIndex = currentAgendaIndex + 1;
                    // Resetting item time happens AFTER sending message
                }
            }
        } else {
             // Handle case where currentItem might be undefined (shouldn't happen with checks)
             currentItemElapsedTime = 0;
             displayItemTime = 0;
        }
    } else {
        // No agenda items or index out of bounds
        currentItemElapsedTime = 0;
        displayItemTime = 0;
    }
    // --- End Agenda Logic ---

    const tickData = {
        type: 'tick',
        totalTime: currentTotalTimeSeconds,
        currentItemTime: Math.floor(displayItemTime), // Send floored integer seconds to UI
        currentAgendaIndex: nextAgendaIndex, // Send potentially updated index
        currentItemCompleted: newItemCompleted,
        playSound: shouldPlaySound,
        showAlert: shouldShowAlert,
    };

    // Send only if data changed (compare floored item time for stability)
    const dataChanged = JSON.stringify(tickData) !== JSON.stringify(lastTickDataSent);
    if (dataChanged) {
        self.postMessage(tickData);
        lastTickDataSent = tickData;
    }

    // --- Update Worker State AFTER sending message ---
    if (newItemCompleted) {
        // Mark the item that just finished as completed
        // Use currentAgendaIndex because nextAgendaIndex might have changed
        if (agendaItems[currentAgendaIndex]) {
            agendaItems[currentAgendaIndex].completed = true;
        }

        // If we are actually moving to the next item
        if (nextAgendaIndex !== currentAgendaIndex) {
            currentAgendaIndex = nextAgendaIndex;
            currentItemElapsedTime = 0; // Reset time for the NEW item
            console.log(`Worker: Moved to item ${currentAgendaIndex}, reset item time.`);
            // Update lastTickDataSent immediately to reflect the reset state
            if (lastTickDataSent) {
                lastTickDataSent.currentItemTime = 0;
                lastTickDataSent.currentAgendaIndex = currentAgendaIndex;
                lastTickDataSent.currentItemCompleted = false; // New item isn't completed
            }
        }
    }

    // Stop timer AFTER sending the final tick message if flagged
    if (timerShouldStop) {
        // Call pauseTimer which handles final state calculation and message
        pauseTimer(true);
    }
}

// --- Message Handler ---
self.onmessage = function(event) {
    const { command, payload } = event.data;
    // console.log("Worker received command:", command, payload);

    switch (command) {
        case 'start':
            if (!isRunning) {
                isRunning = true;
                // If startTime is 0, it means it's a fresh start or after reset
                // If not 0, it means resume, accumulatedTime already holds previous value
                startTime = Date.now();
                lastTickTimestamp = startTime; // Initialize timestamp for delta calculation

                if (payload?.resetAgenda) {
                    console.log("Worker: Resetting agenda and time on start.");
                    currentAgendaIndex = 0;
                    accumulatedTime = 0;
                    currentItemElapsedTime = 0; // Ensure reset
                    agendaItems = payload.agendaItems?.map(item => ({ ...item, completed: false })) || [];
                    plannedDurationSeconds = (payload.plannedDuration || 0) * 60;
                    alertShownForCurrentLimit = false;
                    lastTickDataSent = null;
                } else {
                    console.log("Worker: Resuming timer.");
                    // Ensure accumulatedTime is correct from last pause
                    // currentItemElapsedTime should be correct from last pause state
                }

                if (!intervalId) {
                    // Use a slightly shorter interval for better perceived smoothness
                    intervalId = setInterval(tick, TICK_INTERVAL_MS);
                }
                tick(); // Execute immediately
            }
            break;

        case 'pause':
            if (isRunning) {
                pauseTimer(true); // Use centralized function
            }
            break;

        case 'reset':
            console.log("Worker: Resetting timer state.");
            pauseTimer(false); // Stop without sending 'paused'
            accumulatedTime = 0;
            currentAgendaIndex = 0;
            currentItemElapsedTime = 0; // Ensure reset
            alertShownForCurrentLimit = false;
            // Reset completion status on worker side too
            agendaItems = agendaItems.map(item => ({ ...item, completed: false }));
            lastTickDataSent = null;

            self.postMessage({
                type: 'reset',
                totalTime: 0,
                currentItemTime: 0,
                currentAgendaIndex: 0,
                showAlert: false,
            });
            break;

        case 'updateData':
            console.log("Worker: Updating data.", payload);
            const previousAgendaLength = agendaItems.length;
            const currentCompletedStatus = agendaItems.reduce((acc, item) => {
                if (item) acc[item.id] = item.completed;
                return acc;
            }, {});
            agendaItems = payload.agendaItems?.map(item => ({
                ...item,
                completed: currentCompletedStatus[item.id] || false
            })) || [];
            plannedDurationSeconds = (payload.plannedDuration || 0) * 60;

            // Adjust index if it becomes invalid
            if (currentAgendaIndex >= agendaItems.length) {
                console.warn("Worker: Current agenda index out of bounds after update. Resetting.");
                currentAgendaIndex = Math.max(0, agendaItems.length - 1);
                currentItemElapsedTime = 0; // Reset time if index changed drastically
            }
             // Reset index/time if agenda goes from items to none or vice-versa
            if ((previousAgendaLength === 0 && agendaItems.length > 0) || (previousAgendaLength > 0 && agendaItems.length === 0)) {
                 currentAgendaIndex = 0;
                 currentItemElapsedTime = 0;
            }

            lastTickDataSent = null; // Force update on next tick if running
            // If paused, send updated state reflecting new data
            if (!isRunning) {
                 self.postMessage({
                    type: 'paused',
                    totalTime: accumulatedTime,
                    currentItemTime: Math.floor(Math.min(currentItemElapsedTime, (agendaItems[currentAgendaIndex]?.duration || 0) * 60)), // Send clamped, floored time
                    currentAgendaIndex: currentAgendaIndex,
                    showAlert: calculateShowAlert(accumulatedTime),
                });
            }
            break;

        case 'forceNextItem':
            if (isRunning && currentAgendaIndex < agendaItems.length - 1) {
                console.log("Worker: Force moving to next item.");
                const now = Date.now();
                // Calculate total time at the moment of skip
                const currentTotalRunTimeMs = startTime ? now - startTime : 0;
                const currentTotalTimeSeconds = accumulatedTime + Math.floor(currentTotalRunTimeMs / 1000);

                // Mark current item as completed locally
                if (agendaItems[currentAgendaIndex]) {
                    agendaItems[currentAgendaIndex].completed = true;
                }

                // Move to next item
                currentAgendaIndex++;
                currentItemElapsedTime = 0; // Reset time for the new item
                lastTickTimestamp = now; // Reset timestamp for delta calculation starting now

                console.log(`Worker: Moved to item ${currentAgendaIndex}, reset item time.`);

                const shouldShowAlert = calculateShowAlert(currentTotalTimeSeconds);

                // Send updated state immediately
                const forceTickData = {
                    type: 'tick',
                    totalTime: currentTotalTimeSeconds,
                    currentItemTime: 0, // Send 0
                    currentAgendaIndex: currentAgendaIndex,
                    currentItemCompleted: true, // Signal previous item completion
                    playSound: 'bell',
                    showAlert: shouldShowAlert,
                };
                self.postMessage(forceTickData);
                // Update lastTickDataSent immediately to reflect the reset state
                lastTickDataSent = forceTickData;

            } else {
                console.log("Worker: Ignored FORCE_NEXT_ITEM (not running or already on last item).");
            }
            break;

        case 'ping':
            // Respond to ping to check if worker is alive
            self.postMessage({
                type: 'pong',
                isRunning: isRunning,
                totalTime: accumulatedTime + (isRunning && startTime ? Math.floor((Date.now() - startTime) / 1000) : 0),
                currentItemTime: Math.floor(currentItemElapsedTime),
                currentAgendaIndex: currentAgendaIndex,
                timestamp: Date.now()
            });
            break;

        default:
            console.warn('Worker: Unknown command received:', command);
    }
};

// Handle potential worker suspension/resumption with heartbeat
setInterval(() => {
    const now = Date.now();
    const timeSinceLastHeartbeat = now - lastHeartbeat;

    // If more than 2 seconds have passed since last heartbeat, we might have been suspended
    if (timeSinceLastHeartbeat > 2000 && isRunning) {
        console.log('[Stopwatch Worker] Heartbeat detected potential suspension, recalculating timer');

        // Recalculate and send current state
        if (startTime) {
            const totalElapsedMs = now - startTime;
            const totalElapsedSeconds = Math.floor(totalElapsedMs / 1000);
            const newTotalTime = accumulatedTime + totalElapsedSeconds;

            // Update internal state
            const deltaSeconds = timeSinceLastHeartbeat / 1000;
            if (agendaItems.length > 0 && currentAgendaIndex < agendaItems.length) {
                currentItemElapsedTime += deltaSeconds;
                currentItemElapsedTime = Math.max(0, currentItemElapsedTime);
            }

            // Send recovery message
            self.postMessage({
                type: 'tick',
                totalTime: newTotalTime,
                currentItemTime: Math.floor(currentItemElapsedTime),
                currentAgendaIndex: currentAgendaIndex,
                currentItemCompleted: false,
                playSound: null,
                showAlert: calculateShowAlert(newTotalTime),
                resumed: true
            });

            console.log('[Stopwatch Worker] Recovery state sent after suspension');
        }
    }

    lastHeartbeat = now;
}, 1000); // Check every second

console.log("Stopwatch worker loaded and ready with enhanced background support.");
