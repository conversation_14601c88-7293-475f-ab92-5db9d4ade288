"use client"

import { useRef, useEffect, useState } from "react"
import { useSound } from "@/components/sound-provider"
import { useEnhancedAudioContext, useNotificationPermission } from "@/components/wake-lock-manager"

interface EnhancedSoundOptions {
  fallbackToNotification?: boolean
  notificationTitle?: string
  notificationBody?: string
  priority?: "high" | "normal" | "low"
}

export function useEnhancedSound() {
  const { playSound: originalPlaySound, isSoundEnabled } = useSound()
  const { playEnhancedSound, isAudioUnlocked } = useEnhancedAudioContext()
  const { permission: notificationPermission, sendNotificationToServiceWorker } = useNotificationPermission()
  
  const [lastSoundAttempt, setLastSoundAttempt] = useState<number>(0)
  const soundTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Enhanced play sound with multiple strategies and notification fallback
  const playEnhancedSoundWithFallback = async (
    type: "bell" | "alarm" | "notification" = "bell",
    options: EnhancedSoundOptions = {}
  ) => {
    if (!isSoundEnabled) {
      console.log("[Enhanced Sound] Sound is disabled")
      return false
    }

    const now = Date.now()
    
    // Prevent rapid successive calls (debounce)
    if (now - lastSoundAttempt < 100) {
      console.log("[Enhanced Sound] Debouncing rapid sound calls")
      return false
    }
    setLastSoundAttempt(now)

    // Clear any existing timeout
    if (soundTimeoutRef.current) {
      clearTimeout(soundTimeoutRef.current)
    }

    console.log(`[Enhanced Sound] Attempting to play ${type} sound with enhanced strategies`)

    let soundPlayed = false

    // Strategy 1: Try enhanced audio context with multiple fallbacks
    try {
      soundPlayed = await playEnhancedSound(type)
      if (soundPlayed) {
        console.log("[Enhanced Sound] Successfully played via enhanced audio")
        return true
      }
    } catch (error) {
      console.warn("[Enhanced Sound] Enhanced audio failed:", error)
    }

    // Strategy 2: Try original sound provider
    if (!soundPlayed) {
      try {
        originalPlaySound(type)
        console.log("[Enhanced Sound] Played via original sound provider")
        soundPlayed = true
      } catch (error) {
        console.warn("[Enhanced Sound] Original sound provider failed:", error)
      }
    }

    // Strategy 3: Delayed retry (for cases where audio context needs time)
    if (!soundPlayed && isAudioUnlocked) {
      soundTimeoutRef.current = setTimeout(async () => {
        try {
          const retrySuccess = await playEnhancedSound(type)
          if (retrySuccess) {
            console.log("[Enhanced Sound] Retry successful")
          } else {
            console.warn("[Enhanced Sound] Retry failed")
          }
        } catch (error) {
          console.warn("[Enhanced Sound] Retry error:", error)
        }
      }, 100)
    }

    // Strategy 4: Notification fallback (especially important for PWA with locked screen)
    if (!soundPlayed && options.fallbackToNotification && notificationPermission === "granted") {
      try {
        const title = options.notificationTitle || "Timer Alert"
        const body = options.notificationBody || "Your timer has finished!"
        
        sendNotificationToServiceWorker(
          title,
          body,
          "/icons/icon-192x192.png",
          `timer-sound-fallback-${now}`,
          options.priority === "high"
        )
        
        console.log("[Enhanced Sound] Notification sent as audio fallback")
        return true
      } catch (error) {
        console.error("[Enhanced Sound] Notification fallback failed:", error)
      }
    }

    // Strategy 5: Vibration as last resort (mobile only)
    if (!soundPlayed && typeof navigator !== "undefined" && "vibrate" in navigator) {
      try {
        navigator.vibrate([200, 100, 200, 100, 200])
        console.log("[Enhanced Sound] Vibration used as fallback")
        return true
      } catch (error) {
        console.warn("[Enhanced Sound] Vibration failed:", error)
      }
    }

    if (!soundPlayed) {
      console.error("[Enhanced Sound] All sound strategies failed")
    }

    return soundPlayed
  }

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (soundTimeoutRef.current) {
        clearTimeout(soundTimeoutRef.current)
      }
    }
  }, [])

  return {
    playEnhancedSound: playEnhancedSoundWithFallback,
    isSoundEnabled,
    isAudioUnlocked,
    notificationPermission
  }
}

// Hook specifically for timer completion sounds
export function useTimerSound() {
  const { playEnhancedSound } = useEnhancedSound()

  const playTimerCompletionSound = (timerName?: string) => {
    return playEnhancedSound("bell", {
      fallbackToNotification: true,
      notificationTitle: "Timer Complete!",
      notificationBody: timerName ? `${timerName} has finished` : "Your timer has finished!",
      priority: "high"
    })
  }

  const playIntervalSound = (intervalName?: string, nextInterval?: string) => {
    return playEnhancedSound("bell", {
      fallbackToNotification: true,
      notificationTitle: intervalName ? `${intervalName} Complete` : "Interval Complete",
      notificationBody: nextInterval ? `Next: ${nextInterval}` : "Moving to next interval",
      priority: "normal"
    })
  }

  const playPhaseChangeSound = (phaseName?: string) => {
    return playEnhancedSound("bell", {
      fallbackToNotification: true,
      notificationTitle: "Phase Change",
      notificationBody: phaseName ? `Starting ${phaseName}` : "Phase changed",
      priority: "normal"
    })
  }

  return {
    playTimerCompletionSound,
    playIntervalSound,
    playPhaseChangeSound
  }
}

// Hook for meeting timer sounds
export function useMeetingSound() {
  const { playEnhancedSound } = useEnhancedSound()

  const playAgendaItemSound = (itemName?: string, nextItem?: string) => {
    return playEnhancedSound("bell", {
      fallbackToNotification: true,
      notificationTitle: itemName ? `${itemName} Complete` : "Agenda Item Complete",
      notificationBody: nextItem ? `Next: ${nextItem}` : "Moving to next agenda item",
      priority: "normal"
    })
  }

  const playMeetingCompleteSound = (meetingName?: string, duration?: number) => {
    return playEnhancedSound("bell", {
      fallbackToNotification: true,
      notificationTitle: "Meeting Complete!",
      notificationBody: meetingName 
        ? `${meetingName} finished${duration ? ` in ${Math.floor(duration / 60)} minutes` : ""}`
        : "Your meeting has finished!",
      priority: "high"
    })
  }

  return {
    playAgendaItemSound,
    playMeetingCompleteSound
  }
}

// Hook for workout sounds
export function useWorkoutSound() {
  const { playEnhancedSound } = useEnhancedSound()

  const playExerciseCompleteSound = (exerciseName?: string) => {
    return playEnhancedSound("bell", {
      fallbackToNotification: true,
      notificationTitle: "Exercise Complete!",
      notificationBody: exerciseName ? `${exerciseName} finished` : "Exercise completed",
      priority: "normal"
    })
  }

  const playRestCompleteSound = (nextExercise?: string) => {
    return playEnhancedSound("bell", {
      fallbackToNotification: true,
      notificationTitle: "Rest Complete!",
      notificationBody: nextExercise ? `Next: ${nextExercise}` : "Rest time is over",
      priority: "normal"
    })
  }

  const playWorkoutCompleteSound = (workoutName?: string) => {
    return playEnhancedSound("bell", {
      fallbackToNotification: true,
      notificationTitle: "Workout Complete!",
      notificationBody: workoutName ? `${workoutName} finished!` : "Great job! Workout completed!",
      priority: "high"
    })
  }

  return {
    playExerciseCompleteSound,
    playRestCompleteSound,
    playWorkoutCompleteSound
  }
}
