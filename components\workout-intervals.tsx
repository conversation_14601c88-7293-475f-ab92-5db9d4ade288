// c:\Users\<USER>\Desktop\Apps\stopwatch\components\workout-intervals.tsx
"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { getTranslations } from "@/lib/i18n/translations"
import { Play, Pause, RefreshCw, Plus, Trash2, Save, Clock, Activity, Copy, MoveUp, MoveDown } from "lucide-react" // Assurez-vous que MoveUp/MoveDown sont importés si vous les utilisez
import { useSound } from "@/components/sound-provider"
import { ToolControls } from "@/components/tool-controls"
import { ToolFullscreenWrapper } from "@/components/tool-fullscreen-wrapper"
import { WakeLockManager, useNotificationPermission, useEnhancedAudioContext } from "@/components/wake-lock-manager"

// Clés de stockage local
const STORAGE_KEYS = {
  INTERVALS: "timetools_workout_intervals",
  TEMPLATES: "timetools_workout_templates",
  ACTIVE_WORKOUT: "timetools_active_workout",
}

// Fonctions utilitaires pour le stockage local
function saveToLocalStorage<T>(key: string, data: T): void {
  try {
    if (typeof window !== "undefined") {
      const serializedData = JSON.stringify(data)
      localStorage.setItem(key, serializedData)
    }
  } catch (error) {
    console.error("Error saving to localStorage:", error)
  }
}

function getFromLocalStorage<T>(key: string, defaultValue: T): T {
  try {
    if (typeof window !== "undefined") {
      const serializedData = localStorage.getItem(key)
      if (serializedData === null) {
        return defaultValue
      }
      return JSON.parse(serializedData) as T
    }
    return defaultValue
  } catch (error) {
    console.error("Error getting from localStorage:", error)
    return defaultValue
  }
}

interface WorkoutIntervalsProps {
  lang: string
}

interface WorkoutInterval {
  id: string
  name: string
  duration: number // in seconds
  type: "work" | "rest"
}

interface WorkoutTemplate {
  id: string
  name: string
  intervals: WorkoutInterval[]
  createdAt: string
  lastUsed?: string
}

interface ActiveWorkout {
  templateId?: string
  intervals: WorkoutInterval[]
  currentIntervalIndex: number
  isRunning: boolean
  elapsedTime: number // Temps écoulé dans l'intervalle courant (pour sauvegarde)
}

export function WorkoutIntervals({ lang }: WorkoutIntervalsProps) {
  const t = getTranslations(lang)
  const [activeTab, setActiveTab] = useState("workout")

  // État des intervalles
  const [intervals, setIntervals] = useState<WorkoutInterval[]>([])

  // État du workout actif
  const [isRunning, setIsRunning] = useState(false)
  // currentTime: Temps ÉCOULÉ dans l'intervalle courant (pour sauvegarde/reprise)
  const [currentTime, setCurrentTime] = useState(0)
  // NOUVEL ÉTAT: Temps RESTANT (en ms) pour l'intervalle courant, pour l'affichage direct
  const [currentIntervalTimeLeftMs, setCurrentIntervalTimeLeftMs] = useState(0);
  const [currentIntervalIndex, setCurrentIntervalIndex] = useState(0)
  const [completedIntervals, setCompletedIntervals] = useState<string[]>([])

  // État pour l'ajout d'intervalles
  const [newIntervalName, setNewIntervalName] = useState("")
  const [newIntervalDuration, setNewIntervalDuration] = useState(30)
  const [newIntervalType, setNewIntervalType] = useState<"work" | "rest">("work")

  // État pour les modèles
  const [templates, setTemplates] = useState<WorkoutTemplate[]>([])
  const [newTemplateName, setNewTemplateName] = useState("")
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null)
  const [editingTemplate, setEditingTemplate] = useState<string | null>(null)
  const [isToolFullscreen, setIsToolFullscreen] = useState(false)

  const countdownWorkerRef = useRef<Worker | null>(null)
  const isRunningRef = useRef(isRunning)
  const currentIntervalIndexRef = useRef(currentIntervalIndex)
  const intervalsRef = useRef(intervals)
  const currentIntervalDurationRef = useRef(0); // Ref pour la durée (en ms) de l'intervalle courant

  const { playSound, isSoundEnabled } = useSound()

  // Enhanced notification and wake lock management
  const {
    permission: notificationPermission,
    sendNotificationToServiceWorker
  } = useNotificationPermission()

  const {
    audioContext,
    initializeAudioContext,
    resumeAudioContext
  } = useEnhancedAudioContext()

  // Ajouter une ref pour suivre l'initialisation
  const isInitializedRef = useRef(false);

  // Sauvegarder le titre original de la page au chargement du composant
  const originalTitle = useRef<string>("")

  const animationFrameRef = useRef<number | null>(null)
  const lastTimestampRef = useRef<number | null>(null)
  const startTimeRef = useRef<number | null>(null)
  const intervalStartTimeRef = useRef<number | null>(null)

  // Initialize enhanced audio context on component mount
  useEffect(() => {
    initializeAudioContext()
  }, [initializeAudioContext])

  // --- Update Refs when State Changes ---
  useEffect(() => {
    isRunningRef.current = isRunning
  }, [isRunning])

  useEffect(() => {
    currentIntervalIndexRef.current = currentIntervalIndex
    // Mettre à jour aussi la durée de l'intervalle courant dans une ref
    const currentInterval = intervals[currentIntervalIndex];
    if (currentInterval) {
        currentIntervalDurationRef.current = currentInterval.duration * 1000; // en ms

        // MODIFICATION: Utiliser un ref pour stocker timeLeft calculé plutôt que setState directement ici
        // Condition plus spécifique: uniquement lors d'un changement d'index (pas sur chaque render)
        if (!isRunningRef.current) {
            // Au lieu de setState ici, on définit une ref qui sera utilisée par le timer si nécessaire
            const initialElapsedTimeMs = currentTime * 1000;
            // Stockons la valeur calculée localement pour la réutiliser dans l'effet de chargement initial
            const calculatedTimeLeft = Math.max(0, currentIntervalDurationRef.current - initialElapsedTimeMs);

            // Mise à jour CONDITIONNELLE avec vérification de la valeur précédente pour éviter boucle
            if (Math.abs(calculatedTimeLeft - currentIntervalTimeLeftMs) > 50) { // 50ms de tolérance
                setCurrentIntervalTimeLeftMs(calculatedTimeLeft);
            }
        }
    } else {
        currentIntervalDurationRef.current = 0;
        // Même chose ici - mise à jour conditionnelle
        if (!isRunningRef.current && currentIntervalTimeLeftMs !== 0) {
            setCurrentIntervalTimeLeftMs(0);
        }
    }
  }, [currentIntervalIndex, intervals, currentTime, currentIntervalTimeLeftMs]) // Added currentTime and currentIntervalTimeLeftMs to dependencies

  useEffect(() => {
    intervalsRef.current = intervals
    // Mettre à jour la durée ref si les intervalles changent et affectent l'index courant
    const currentInterval = intervals[currentIntervalIndexRef.current];
    if (currentInterval) {
        currentIntervalDurationRef.current = currentInterval.duration * 1000;
    } else {
        currentIntervalDurationRef.current = 0;
    }
  }, [intervals])

  // --- Initialize Worker ---
  useEffect(() => {
    if (typeof window !== "undefined" && !countdownWorkerRef.current) {
      try {
        const worker = new Worker("/workers/countdown-worker.js")
        countdownWorkerRef.current = worker
        console.log("Countdown Worker initialized for WorkoutIntervals.")

        // Gestionnaire de message - défini directement ici mais avec references stables
        worker.onmessage = (event) => {
          const { type, timeLeft } = event.data // timeLeft est en ms

          if (type === "tick") {
            // --- MISE À JOUR DIRECTE POUR L'AFFICHAGE ---
            const roundedTimeLeft = Math.round(timeLeft);
            setCurrentIntervalTimeLeftMs(roundedTimeLeft);

            // --- MISE À JOUR DE currentTime (temps écoulé) pour la sauvegarde ---
            const currentDurationMs = currentIntervalDurationRef.current;
            if (currentDurationMs > 0) {
                const elapsedSeconds = Math.max(0, Math.ceil((currentDurationMs - timeLeft) / 1000));
                setCurrentTime(prev => prev !== elapsedSeconds ? elapsedSeconds : prev);
            } else {
                 setCurrentTime(0);
            }

            // --- Logique de Transition ---
            if (timeLeft <= 0 && isRunningRef.current) {
              console.log("Worker indicated interval end.");
              worker.postMessage({ command: "stop" }) // Arrêt temporaire

              // Enhanced audio handling with context resumption
              if (isSoundEnabled) {
                try {
                  // Try to resume audio context if suspended
                  if (audioContext && audioContext.state === "suspended") {
                    resumeAudioContext().then(() => {
                      playSound("bell");
                      console.log("Workout interval ended, audio context resumed and sound played.");
                    }).catch((error) => {
                      console.warn("Could not resume audio context:", error);
                      playSound("bell"); // Try anyway
                    });
                  } else {
                    playSound("bell");
                    console.log("Workout interval ended, playing sound.");
                  }
                } catch (error) {
                  console.warn("Could not play workout interval sound:", error);
                }
              }

              const currentIdx = currentIntervalIndexRef.current;
              const currentIntervals = intervalsRef.current;

              // Enhanced notification handling
              const currentInterval = currentIntervals[currentIdx];
              if (currentInterval && notificationPermission === "granted") {
                const nextIndex = currentIdx + 1;
                const nextInterval = currentIntervals[nextIndex];

                const notificationTitle = `${t.intervalCompleted || "Intervalle terminé"}: ${currentInterval.name}`;
                const notificationBody = nextInterval
                  ? `${t.nextInterval || "Prochain"}: ${nextInterval.name} (${nextInterval.duration}s)`
                  : t.workoutCompleted || "Entraînement terminé!";

                try {
                  sendNotificationToServiceWorker(
                    notificationTitle,
                    notificationBody,
                    "/icons/icon-192x192.png",
                    "workout-interval-complete",
                    false // Don't require interaction for workout intervals
                  );
                  console.log("Workout interval notification sent to service worker.");
                } catch (error) {
                  console.error("Error sending workout interval notification:", error);
                }
              }

              // Marquer comme complété
              setCompletedIntervals((prev) => {
                  const currentId = currentIntervals[currentIdx]?.id;
                  return (currentId && !prev.includes(currentId)) ? [...prev, currentId] : prev;
              });

              const nextIndex = currentIdx + 1;

              if (nextIndex >= currentIntervals.length) {
                // Workout terminé
                console.log("Workout complete via worker.");
                setIsRunning(false)
                setCurrentTime(0)
                setCurrentIntervalTimeLeftMs(0);
              } else {
                // Interval suivant
                console.log(`Moving to interval ${nextIndex} via worker.`);
                const nextInterval = currentIntervals[nextIndex];
                const nextIntervalDurationMs = nextInterval.duration * 1000;

                // Mettre à jour l'état
                setCurrentIntervalIndex(nextIndex);
                setCurrentTime(0);
                setCurrentIntervalTimeLeftMs(nextIntervalDurationMs);

                // Redémarrer le worker
                if (nextIntervalDurationMs > 0) {
                  const nextEndTime = Date.now() + nextIntervalDurationMs;
                  console.log(`Starting worker for next interval (duration: ${nextInterval.duration}s)`)
                  worker.postMessage({ command: "start", value: nextEndTime });
                } else {
                  console.warn("Next interval has zero duration. Stopping timer.");
                  setIsRunning(false);
                }
              }
            }
          } else {
            console.log("Received non-tick message from worker:", event.data)
          }
        }

        worker.onerror = (error) => {
          console.error("Countdown Worker Error:", error)
          setIsRunning(false)
          setCurrentTime(0)
          setCurrentIntervalTimeLeftMs(0);
        }
      } catch (error) {
        console.error("Failed to initialize countdown worker:", error)
      }
    }

    // --- Cleanup ---
    return () => {
      if (countdownWorkerRef.current) {
        console.log("Terminating countdown worker for WorkoutIntervals...")
        countdownWorkerRef.current.postMessage({ command: "stop" })
        countdownWorkerRef.current.terminate()
        countdownWorkerRef.current = null
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []) // Aucune dépendance pour éviter de recréer le gestionnaire

  // Charger les données depuis localStorage - seulement une fois au montage + quand t change
  useEffect(() => {
    // Éviter les initialisations multiples qui pourraient causer des boucles
    if (isInitializedRef.current) {
      return;
    }

    console.log("Initializing from localStorage");
    const activeWorkout = getFromLocalStorage<ActiveWorkout | null>(STORAGE_KEYS.ACTIVE_WORKOUT, null)
    let loadedIntervals: WorkoutInterval[] = []
    let initialIndex = 0
    let initialElapsedTime = 0 // Temps écoulé chargé (en secondes)
    let initialCompleted: string[] = []
    let initialSelectedTemplate: string | null = null

    if (activeWorkout) {
      loadedIntervals = activeWorkout.intervals
      initialIndex = activeWorkout.currentIntervalIndex
      initialElapsedTime = activeWorkout.elapsedTime

      // Validation de l'index et du temps écoulé
      if (initialIndex >= loadedIntervals.length) {
        console.warn("Loaded active workout has invalid index, resetting.");
        initialIndex = 0
        initialElapsedTime = 0
      } else {
        const loadedIntervalDuration = loadedIntervals[initialIndex]?.duration ?? 0
        // Si le temps écoulé sauvegardé >= durée, on considère l'intervalle comme fini et on avance
        if (initialElapsedTime >= loadedIntervalDuration && loadedIntervalDuration > 0) {
          console.log("Loaded time indicates interval completion, advancing.")
          // Marquer comme complété seulement si l'ID existe
          if (loadedIntervals[initialIndex]?.id) {
            initialCompleted.push(loadedIntervals[initialIndex].id)
          }
          initialIndex++
          initialElapsedTime = 0 // Reset pour le suivant
          // Vérifier si on a dépassé la fin après incrémentation
          if (initialIndex >= loadedIntervals.length) {
            initialIndex = 0 // Ou gérer la fin du workout (ex: rester sur le dernier index, isRunning false)
            // Pour l'instant, on boucle au début
          }
        }
      }

      // Marquer les intervalles précédents comme complétés
      for (let i = 0; i < initialIndex; i++) {
        if (loadedIntervals[i]?.id && !initialCompleted.includes(loadedIntervals[i].id)) {
          initialCompleted.push(loadedIntervals[i].id)
        }
      }

      initialSelectedTemplate = activeWorkout.templateId || null
      // IMPORTANT: Toujours charger en pause
      setIsRunning(false)
    } else {
      // Pas de workout actif, charger depuis INTERVALS ou TEMPLATES par défaut
      const savedIntervals = getFromLocalStorage<WorkoutInterval[]>(STORAGE_KEYS.INTERVALS, [])
      if (savedIntervals.length > 0) {
        loadedIntervals = savedIntervals
      }
    }

    // Charger les templates une seule fois
    let templatesLoaded = false;
    const savedTemplates = getFromLocalStorage<WorkoutTemplate[]>(STORAGE_KEYS.TEMPLATES, [])
    if (savedTemplates.length > 0) {
      setTemplates(savedTemplates)
      templatesLoaded = true;
      if (initialSelectedTemplate) {
        setSelectedTemplate(initialSelectedTemplate)
      }
    }

    // Si pas de template, créer un par défaut
    if (!templatesLoaded) {
      // Créer un template par défaut si aucun n'existe
      const defaultTemplate: WorkoutTemplate = {
        id: "default-hiit-4min",
        name: "HIIT 4 minutes",
        intervals: [
          { id: "def-1", name: t.warmUp || "Warm Up", duration: 60, type: "rest" },
          { id: "def-2", name: t.sprint || "Sprint", duration: 20, type: "work" },
          { id: "def-3", name: t.recovery || "Recovery", duration: 10, type: "rest" },
          { id: "def-4", name: t.sprint || "Sprint", duration: 20, type: "work" },
          { id: "def-5", name: t.recovery || "Recovery", duration: 10, type: "rest" },
          { id: "def-6", name: t.sprint || "Sprint", duration: 20, type: "work" },
          { id: "def-7", name: t.recovery || "Recovery", duration: 10, type: "rest" },
          { id: "def-8", name: t.sprint || "Sprint", duration: 20, type: "work" },
          { id: "def-9", name: t.recovery || "Recovery", duration: 10, type: "rest" },
          { id: "def-10", name: t.stretching || "Stretching", duration: 60, type: "rest" },
        ],
        createdAt: new Date().toISOString(),
      }
      setTemplates([defaultTemplate])
      saveToLocalStorage(STORAGE_KEYS.TEMPLATES, [defaultTemplate])
      // Si aucun intervalle n'a été chargé, utiliser ceux du template par défaut
      if (loadedIntervals.length === 0) {
        loadedIntervals = [...defaultTemplate.intervals]
        setSelectedTemplate(defaultTemplate.id)
      }
    }

    // Préparer les valeurs initiales pour l'affichage
    const initialIntervalDuration = loadedIntervals[initialIndex]?.duration ?? 0
    const initialTimeLeftMs = Math.max(0, (initialIntervalDuration - initialElapsedTime) * 1000)

    // Créer un intervalle de secours si nécessaire
    if (loadedIntervals.length === 0) {
        console.log("No intervals loaded, creating a single default interval.");
        const fallbackInterval: WorkoutInterval = { id: "fallback-1", name: t.work || "Work", duration: 30, type: "work" };
        loadedIntervals = [fallbackInterval];
        saveToLocalStorage(STORAGE_KEYS.INTERVALS, [fallbackInterval]);
    }

    // Au lieu d'utiliser une fonction batchUpdates et setTimeout,
    // utiliser requestAnimationFrame pour éviter les mises à jour pendant le rendu
    requestAnimationFrame(() => {
      // --- Initialiser l'état après chargement ---
      setIntervals(loadedIntervals)
      setCurrentIntervalIndex(initialIndex)
      setCurrentTime(initialElapsedTime)
      setCompletedIntervals(initialCompleted)
      setCurrentIntervalTimeLeftMs(initialTimeLeftMs)

      // Marquer comme initialisé
      isInitializedRef.current = true;
    });

  }, [t]) // Dépendance t pour les traductions par défaut

  // Sauvegarder l'état du workout actif (utilise currentTime)
  useEffect(() => {
    // Utiliser un délai pour éviter des sauvegardes trop fréquentes
    // qui pourraient créer des boucles de rendu
    const timeoutId = setTimeout(() => {
      if (intervals.length > 0) {
        const activeWorkout: ActiveWorkout = {
          templateId: selectedTemplate || undefined,
          intervals,
          currentIntervalIndex,
          isRunning, // Sauvegarde l'état (mais charge toujours en pause)
          elapsedTime: currentTime, // Sauvegarde le temps écoulé
        }
        saveToLocalStorage(STORAGE_KEYS.ACTIVE_WORKOUT, activeWorkout)
      } else {
        localStorage.removeItem(STORAGE_KEYS.ACTIVE_WORKOUT);
      }
    }, 300);

    return () => clearTimeout(timeoutId);

  }, [intervals, currentIntervalIndex, isRunning, currentTime, selectedTemplate])

  // --- Timer Control Functions ---
  const toggleTimer = useCallback(() => {
    if (intervals.length === 0 || !countdownWorkerRef.current) {
        console.warn("Cannot toggle timer: No intervals or worker not ready.");
        return;
    }

    const newIsRunning = !isRunningRef.current; // Utiliser la ref pour la valeur immédiate

    // Au lieu de mettre à jour directement l'état, utilisons un setTimeout
    // pour éviter une mise à jour pendant le rendu
    requestAnimationFrame(() => {
      setIsRunning(newIsRunning); // Mettre à jour l'état React après le rendu actuel
    });

    if (newIsRunning) {
      // Démarrage ou Reprise
      const currentIdx = currentIntervalIndexRef.current; // Utiliser ref
      const currentInterval = intervalsRef.current[currentIdx];

      if (!currentInterval) {
          console.error("Cannot start: Invalid current interval.");
          requestAnimationFrame(() => setIsRunning(false)); // Revert state de manière sûre
          return;
      }

      // Utiliser le temps restant actuel
      let timeToStartMs = currentIntervalTimeLeftMs;

      // Si on démarre (pas une reprise) ou si timeLeft est 0, utiliser la durée complète
      // On considère un démarrage si le temps écoulé (currentTime) est 0
      const isStartingAfresh = currentTime === 0;
      if (isStartingAfresh || timeToStartMs <= 0) {
          timeToStartMs = currentInterval.duration * 1000;
          // Si on redémarre de zéro, s'assurer que les états sont cohérents
          if (isStartingAfresh) {
              requestAnimationFrame(() => {
                setCurrentIntervalTimeLeftMs(timeToStartMs);
                setCurrentTime(0);
              });
          }
      }

      if (timeToStartMs <= 0 && currentInterval.duration > 0) {
          console.warn("Attempting to start timer with zero or negative time left for a non-zero duration interval.");
          requestAnimationFrame(() => setIsRunning(false));
          return;
      }
       if (currentInterval.duration <= 0) {
           console.warn("Attempting to start timer for zero duration interval. Skipping.");
           requestAnimationFrame(() => setIsRunning(false));
           return;
       }

      const endTime = Date.now() + timeToStartMs;
      console.log(`Posting 'start' to worker. EndTime: ${new Date(endTime).toLocaleTimeString()}, Starting with: ${timeToStartMs / 1000}s left`)
      countdownWorkerRef.current.postMessage({ command: "start", value: endTime });

    } else {
      // Pause
      console.log("Posting 'stop' to worker.");
      countdownWorkerRef.current.postMessage({ command: "stop" });
      // currentTime et currentIntervalTimeLeftMs conservent leur dernière valeur
    }
  }, [intervals.length, currentIntervalTimeLeftMs, currentTime]);

  // Mettre à jour la ref isRunning directement quand l'état change
  useEffect(() => {
    isRunningRef.current = isRunning;
  }, [isRunning]);

  const resetTimer = useCallback(() => {
    if (countdownWorkerRef.current) {
      console.log("Reset: Posting 'stop' to worker.")
      countdownWorkerRef.current.postMessage({ command: "stop" })
    }

    // Utiliser requestAnimationFrame pour éviter les mises à jour d'état pendant le rendu
    requestAnimationFrame(() => {
      setIsRunning(false)
      setCurrentTime(0)
      setCurrentIntervalIndex(0)
      setCompletedIntervals([])

      // Réinitialiser l'affichage du temps restant à la durée du premier intervalle
      const firstIntervalDuration = intervalsRef.current[0]?.duration ?? 0;
      const firstIntervalDurationMs = firstIntervalDuration * 1000;
      setCurrentIntervalTimeLeftMs(firstIntervalDurationMs);

      localStorage.removeItem(STORAGE_KEYS.ACTIVE_WORKOUT)
    });

  }, [/* Dépendances minimales */]);

  // --- Fonctions de gestion des intervalles (add, remove, move) ---
  // IMPORTANT: S'assurer que add/remove/move appellent resetTimer() pour éviter incohérences
  const addInterval = () => {
    if (newIntervalName.trim() === "") return

    const newInterval: WorkoutInterval = {
      id: `int-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`,
      name: newIntervalName,
      duration: newIntervalDuration,
      type: newIntervalType,
    }

    resetTimer(); // Appeler reset AVANT modification structurelle
    setIntervals(prevIntervals => [...prevIntervals, newInterval]);
    setNewIntervalName("")
    setNewIntervalDuration(30)
    setNewIntervalType("work")
    setSelectedTemplate(null); // L'ajout crée un workout personnalisé
  }

  const removeInterval = (id: string) => {
    const indexToRemove = intervals.findIndex((interval) => interval.id === id);
    if (indexToRemove === -1) return;
    // Empêcher la suppression du dernier intervalle ? Optionnel.
    // if (intervals.length <= 1) return;

    // Reset si l'intervalle supprimé est celui en cours ou un précédent
    // Ou simplement toujours reset pour la simplicité
    resetTimer(); // Reset AVANT la modification

    setIntervals(prevIntervals => prevIntervals.filter((interval) => interval.id !== id));
    setSelectedTemplate(null); // La suppression crée un workout personnalisé

    // Pas besoin d'ajuster l'index manuellement car resetTimer() remet à 0
  }

  const moveIntervalUp = (id: string) => {
    const index = intervals.findIndex((interval) => interval.id === id)
    if (index <= 0) return

    resetTimer(); // Reset AVANT la modification

    const newIntervals = [...intervals];
    [newIntervals[index], newIntervals[index - 1]] = [newIntervals[index - 1], newIntervals[index]]; // Swap

    setIntervals(newIntervals);
    setSelectedTemplate(null); // La modification crée un workout personnalisé
  }

  const moveIntervalDown = (id: string) => {
    const index = intervals.findIndex((interval) => interval.id === id)
    if (index >= intervals.length - 1 || index < 0) return

    resetTimer(); // Reset AVANT la modification

    const newIntervals = [...intervals];
    [newIntervals[index], newIntervals[index + 1]] = [newIntervals[index + 1], newIntervals[index]]; // Swap

    setIntervals(newIntervals);
    setSelectedTemplate(null); // La modification crée un workout personnalisé
  }

  // --- Fonctions de gestion des templates (save, update, load, delete) ---
  const saveTemplate = () => {
    if (newTemplateName.trim() === "" || intervals.length === 0) return

    const newTemplate: WorkoutTemplate = {
      id: `tmpl-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`,
      name: newTemplateName,
      intervals: [...intervals], // Créer une copie
      createdAt: new Date().toISOString(),
    }

    const updatedTemplates = [...templates, newTemplate]
    setTemplates(updatedTemplates)
    saveToLocalStorage(STORAGE_KEYS.TEMPLATES, updatedTemplates)
    setNewTemplateName("")
    setSelectedTemplate(newTemplate.id) // Sélectionner le nouveau template
  }

   const updateTemplate = (templateId: string) => {
    if (!templateId || intervals.length === 0) return

    const templateToUpdate = templates.find(t => t.id === templateId);
    if (!templateToUpdate) return;

    // Demander confirmation si le nom est différent ? Optionnel.
    const updatedTemplates = templates.map((template) =>
      template.id === templateId
        ? {
            ...template,
            name: template.name, // Conserver le nom existant lors de la mise à jour
            intervals: [...intervals], // Mettre à jour avec les intervalles actuels
            lastUsed: template.lastUsed, // Conserver lastUsed
          }
        : template,
    )

    setTemplates(updatedTemplates)
    saveToLocalStorage(STORAGE_KEYS.TEMPLATES, updatedTemplates)
    setEditingTemplate(null) // Sortir du mode édition si applicable
    setSelectedTemplate(templateId) // Garder le template sélectionné
    // Pas besoin de resetTimer, on met juste à jour la définition du template
  }

  const loadTemplate = (templateId: string) => {
    const template = templates.find((t) => t.id === templateId)
    if (!template) return

    resetTimer(); // Reset AVANT de charger les nouveaux intervalles

    // Mettre à jour la date de dernière utilisation
    const updatedTemplates = templates.map((t) =>
      t.id === templateId ? { ...t, lastUsed: new Date().toISOString() } : t,
    )
    setTemplates(updatedTemplates)
    saveToLocalStorage(STORAGE_KEYS.TEMPLATES, updatedTemplates)

    // Charger les intervalles depuis le template (copie profonde)
    const templateIntervals = JSON.parse(JSON.stringify(template.intervals)) as WorkoutInterval[];
    setIntervals(templateIntervals)
    setSelectedTemplate(templateId)
    // Les états (index, time, completed, timeLeftMs) sont gérés par resetTimer() et les useEffect dépendants
  }

  const duplicateTemplate = (templateId: string) => {
    const template = templates.find((t) => t.id === templateId)
    if (!template) return

    const newTemplate: WorkoutTemplate = {
      ...JSON.parse(JSON.stringify(template)), // Copie profonde
      id: `tmpl-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`, // ID unique
      name: `${template.name} (${t.copy || "Copy"})`,
      createdAt: new Date().toISOString(), // Nouvelle date de création
      lastUsed: undefined, // Reset last used pour la copie
    }

    const updatedTemplates = [...templates, newTemplate]
    setTemplates(updatedTemplates)
    saveToLocalStorage(STORAGE_KEYS.TEMPLATES, updatedTemplates)
  }

  const deleteTemplate = (templateId: string) => {
    const isDeletingSelected = selectedTemplate === templateId;

    const updatedTemplates = templates.filter((t) => t.id !== templateId)
    setTemplates(updatedTemplates)
    saveToLocalStorage(STORAGE_KEYS.TEMPLATES, updatedTemplates)

    if (isDeletingSelected) {
        resetTimer(); // Reset l'état du workout
        setSelectedTemplate(null); // Désélectionner
        // Charger le premier template restant ou vider les intervalles
        if (updatedTemplates.length > 0) {
            // Option 1: Charger le premier
            // loadTemplate(updatedTemplates[0].id);
            // Option 2: Vider les intervalles (plus sûr pour éviter confusion)
             setIntervals([]);
             localStorage.removeItem(STORAGE_KEYS.ACTIVE_WORKOUT);
        } else {
             setIntervals([]); // Aucun template restant, vider les intervalles
             localStorage.removeItem(STORAGE_KEYS.ACTIVE_WORKOUT);
        }
    }

    if (editingTemplate === templateId) {
      setEditingTemplate(null)
    }
  }

  // --- Fonctions Utilitaires ---
  const formatTranslation = (template: string | undefined, values: Record<string, string>): string => {
    if (!template) return "";
    let formatted = template;
    for (const key in values) {
      formatted = formatted.replace(`{${key}}`, values[key]);
    }
    return formatted;
  };

  const formatTime = (ms: number) => {
    // Arrondir au supérieur pour l'affichage du compte à rebours
    const totalSeconds = Math.ceil(Math.max(0, ms) / 1000);
    const mins = Math.floor(totalSeconds / 60);
    const secs = totalSeconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  }

  const getTotalDurationSeconds = () => {
    return intervals.reduce((total, interval) => total + interval.duration, 0)
  }

  // Calcul du temps restant total (utilise l'état d'affichage timeLeftMs)
  const getTotalRemainingTimeMs = () => {
    if (currentIntervalIndex >= intervals.length) return 0;

    let remainingMs = currentIntervalTimeLeftMs; // Temps restant dans l'intervalle courant

    // Ajouter la durée des intervalles suivants
    for (let i = currentIntervalIndex + 1; i < intervals.length; i++) {
      remainingMs += intervals[i].duration * 1000;
    }
    return Math.max(0, remainingMs);
  }

  // --- Calculs pour l'UI ---
  const currentInterval = intervals[currentIntervalIndex];
  const currentIntervalDurationSeconds = currentInterval?.duration ?? 0;
  const currentIntervalDurationMs = currentIntervalDurationSeconds * 1000;
  // Le progrès est basé sur le temps restant par rapport à la durée totale de l'intervalle
  const progress = currentIntervalDurationMs > 0
    ? ( (currentIntervalDurationMs - Math.max(0, currentIntervalTimeLeftMs)) / currentIntervalDurationMs ) * 100
    : (isRunning ? 100 : 0); // Si durée 0, progrès 100% si en cours, sinon 0
  // Le temps affiché est directement formaté depuis currentIntervalTimeLeftMs
  const currentIntervalTimeDisplayFormatted = formatTime(currentIntervalTimeLeftMs);

  // Sauvegarder le titre original de la page au chargement du composant
  useEffect(() => {
    originalTitle.current = document.title
    return () => {
      document.title = originalTitle.current
    }
  }, [])

  // Mettre à jour le titre de l'onglet du navigateur avec le temps restant
  useEffect(() => {
    if (isRunning && intervals.length > 0) {
      const currentInterval = intervals[currentIntervalIndex]
      if (currentInterval) {
        const formattedTime = formatTime(currentIntervalTimeLeftMs)
        document.title = `${formattedTime} - ${currentInterval.name} - ${t.workout || "Entraînement"}`
      }
    } else {
      document.title = originalTitle.current
    }
  }, [currentIntervalTimeLeftMs, isRunning, intervals, currentIntervalIndex, t])

  const resetWorkout = () => {
    // Arrêter l'animation frame
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current)
      animationFrameRef.current = null
    }

    // Réinitialiser l'état
    setIsRunning(false)
    setCurrentIntervalIndex(0)
    setCurrentTime(0)
    setCurrentIntervalTimeLeftMs(0)
    lastTimestampRef.current = null
    startTimeRef.current = null
    intervalStartTimeRef.current = null

    // Réinitialiser le temps restant au temps du premier intervalle
    if (intervals.length > 0) {
      setCurrentIntervalTimeLeftMs(intervals[0].duration * 1000)
    } else {
      setCurrentIntervalTimeLeftMs(0)
    }

    // Restaurer le titre original
    document.title = originalTitle.current
  }

  // --- Rendu ---
  const mainContent = (
    <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          {/* --- MODIFIED TabsList --- */}
          {/* Using Grid for explicit column control per breakpoint */}
          <TabsList className="grid grid-cols-1 sm:grid-cols-3 gap-2 w-full mb-6 h-auto">
            {/* Removed w-full/sm:flex-1, added justify-center */}
            <TabsTrigger value="workout" className="flex items-center justify-center gap-2">
              <Activity className="h-4 w-4" />
              {t.workout}
            </TabsTrigger>
            <TabsTrigger value="intervals" className="flex items-center justify-center gap-2">
              <Clock className="h-4 w-4" />
              {t.intervals}
            </TabsTrigger>
            <TabsTrigger value="templates" className="flex items-center justify-center gap-2">
              <Save className="h-4 w-4" />
              {t.templates}
            </TabsTrigger>
          </TabsList>
          {/* --- END MODIFIED TabsList --- */}

          {/* Onglet Workout */}
          <TabsContent value="workout">
            {intervals.length > 0 && currentInterval ? (
              <div className="mb-8">
                {/* Infos Intervalle */}
                <div className="flex justify-between items-center mb-2 flex-wrap gap-x-4 gap-y-1">
                  <div>
                    <h3 className="text-xl font-bold">{currentInterval.name}</h3>
                    <p className="text-sm text-muted-foreground">
                      {t.interval} {currentIntervalIndex + 1} / {intervals.length}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <span
                      className={`px-2 py-1 rounded text-xs font-medium ${
                        currentInterval.type === "work"
                          ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                          : "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                      }`}
                    >
                      {currentInterval.type === "work" ? t.work : t.rest}
                    </span>
                    <span className="text-sm font-medium whitespace-nowrap">
                      {formatTime(getTotalRemainingTimeMs())} {t.remaining || "remaining"}
                    </span>
                  </div>
                </div>

                {/* Barre de Progression */}
                <div className="w-full h-4 bg-muted rounded-full mb-4 overflow-hidden">
                  <motion.div
                    className={`h-full ${currentInterval.type === "work" ? "bg-red-500" : "bg-green-500"}`}
                    initial={{ width: "0%" }}
                    animate={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
                    transition={{ duration: 0.1, ease: "linear" }} // Transition rapide
                  />
                </div>

                {/* Affichage du Temps */}
                <div className="text-6xl md:text-7xl font-mono font-bold text-center mb-8">
                  {currentIntervalTimeDisplayFormatted}
                </div>

                {/* Contrôles */}
                <div className="flex justify-center gap-6">
                  <Button
                    onClick={toggleTimer}
                    size="lg"
                    className="w-20 h-20 rounded-full"
                    variant={isRunning ? "destructive" : "default"}
                    disabled={!countdownWorkerRef.current}
                    aria-label={isRunning ? t.pause : t.play}
                  >
                    {isRunning ? <Pause className="h-8 w-8" /> : <Play className="h-8 w-8" />}
                  </Button>
                  <Button
                    onClick={resetTimer}
                    size="lg"
                    variant="outline"
                    className="w-18 h-18 rounded-full"
                    aria-label={t.reset}
                  >
                    <RefreshCw className="h-7 w-7" />
                  </Button>
                </div>

                {/* Durée Totale */}
                <div className="mt-6 text-center">
                  <p className="text-sm text-muted-foreground">
                    {t.totalDuration}: {formatTime(getTotalDurationSeconds() * 1000)}
                  </p>
                </div>

                {/* Progression Workout (Badges) */}
                <div className="mt-6 border-t pt-4">
                  <h4 className="font-medium mb-3">{t.workoutProgress || "Workout Progress"}</h4>
                  <div className="flex flex-wrap gap-2">
                    {intervals.map((interval, index) => {
                      const isCompleted = completedIntervals.includes(interval.id)
                      const isCurrent = index === currentIntervalIndex

                      let badgeVariant: "default" | "secondary" | "outline" = "secondary"
                      if (isCompleted) badgeVariant = "outline"
                      if (isCurrent && !isCompleted) badgeVariant = "default" // Highlight current même en pause

                      let badgeClass = interval.type === "work"
                          ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                          : "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";

                      if (isCompleted) {
                           badgeClass = interval.type === "work"
                            ? "bg-red-100/50 text-red-800/70 dark:bg-red-900/50 dark:text-red-200/70 border-red-200/50 dark:border-red-800/50"
                            : "bg-green-100/50 text-green-800/70 dark:bg-green-900/50 dark:text-green-200/70 border-green-200/50 dark:border-green-800/50";
                      } else if (isCurrent) {
                          // Style spécifique pour l'intervalle courant (non complété)
                          badgeClass += " ring-2 ring-primary/80 ring-offset-1 dark:ring-offset-background"
                      }

                      return (
                        <Badge
                          key={interval.id}
                          variant={badgeVariant}
                          className={`${badgeClass} transition-all`}
                          aria-current={isCurrent ? "step" : undefined}
                        >
                          {interval.name} ({formatTime(interval.duration * 1000)})
                        </Badge>
                      )
                    })}
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground mb-8">
                {intervals.length === 0 ? t.noIntervalsYet : t.loading || "Loading..."}
              </div>
            )}

            {/* Actions Rapides (Chargement Template) */}
            <div className="border-t pt-4">
              <h4 className="font-medium mb-3 text-center">{t.loadQuickTemplate || "Quick Load"}</h4>
              <div className="flex flex-wrap gap-2 justify-center">
                {templates.slice(0, 5).map((template) => (
                  <Button
                    key={template.id}
                    variant="outline"
                    size="sm"
                    onClick={() => loadTemplate(template.id)}
                    className={selectedTemplate === template.id ? "border-primary ring-1 ring-primary" : ""}
                  >
                    {template.name}
                  </Button>
                ))}
                <Button variant="outline" size="sm" onClick={() => setActiveTab("templates")}>
                  {t.moreTemplates || "More Templates"}...
                </Button>
              </div>
            </div>
          </TabsContent>

          {/* Onglet Intervalles */}
          <TabsContent value="intervals">
            <div className="space-y-6">
              <div className="flex justify-between items-center mb-4 flex-wrap gap-2">
                <h3 className="font-medium">{t.intervals}</h3>
                {selectedTemplate && templates.find(t => t.id === selectedTemplate) && (
                  <Button variant="outline" size="sm" onClick={() => updateTemplate(selectedTemplate)}>
                    <Save className="h-4 w-4 mr-2" />
                    {formatTranslation(t.updateTemplateName || 'Update "{name}"', { name: templates.find(t => t.id === selectedTemplate)?.name || '' })}
                  </Button>
                )}
              </div>

              {/* Liste des Intervalles */}
              <div className="space-y-3 mb-6 max-h-[400px] overflow-y-auto pr-2">
                 {intervals.length === 0 ? (
                     <p className="text-center text-muted-foreground py-4">{t.noIntervalsDefine || "No intervals defined."}</p>
                 ) : (
                    intervals.map((interval, index) => (
                      <div
                        key={interval.id}
                        className={`flex flex-col sm:flex-row justify-between sm:items-center p-3 border rounded-md gap-2 ${
                          interval.type === "work"
                            ? "border-red-200 dark:border-red-800 bg-red-50/30 dark:bg-red-900/10"
                            : "border-green-200 dark:border-green-800 bg-green-50/30 dark:bg-green-900/10"
                        }`}
                      >
                        {/* Détails Intervalle */}
                        <div className="flex-grow">
                          <div className="flex items-center gap-2 flex-wrap">
                            <span className="font-medium text-muted-foreground w-6 text-right">{index + 1}.</span>
                            <p className="font-medium">{interval.name}</p>
                            <span
                              className={`px-2 py-0.5 rounded text-xs ${
                                interval.type === "work"
                                  ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                                  : "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                              }`}
                            >
                              {interval.type === "work" ? t.work : t.rest}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground pl-8 sm:pl-0">{formatTime(interval.duration * 1000)}</p>
                        </div>
                        {/* Boutons d'Action */}
                        <div className="flex items-center gap-1 flex-shrink-0 justify-end sm:justify-normal">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => moveIntervalUp(interval.id)}
                            disabled={index === 0}
                            className="h-8 w-8"
                            aria-label={t.moveUp || "Move Up"}
                          >
                            {/* Utiliser l'icône importée ou un SVG inline */}
                            <MoveUp className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => moveIntervalDown(interval.id)}
                            disabled={index === intervals.length - 1}
                            className="h-8 w-8"
                             aria-label={t.moveDown || "Move Down"}
                         >
                            <MoveDown className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => removeInterval(interval.id)}
                            className="h-8 w-8 text-destructive hover:bg-destructive/10 hover:text-destructive"
                            // disabled={intervals.length <= 1} // Optionnel: garder au moins un intervalle
                            aria-label={t.delete || "Delete"}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))
                 )}
              </div>

              {/* Formulaire Ajout Intervalle */}
              <div className="border-t pt-6">
                 <h4 className="font-medium mb-4">{t.addNewInterval || "Add New Interval"}</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div>
                    <Label htmlFor="interval-name">{t.name} <span className="text-red-500">*</span></Label>
                    <Input
                      id="interval-name"
                      placeholder={t.intervalNamePlaceholder || "e.g., High Knees"}
                      value={newIntervalName}
                      onChange={(e) => setNewIntervalName(e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="interval-duration">{t.durationSeconds}</Label>
                    <Input
                      id="interval-duration"
                      type="number"
                      min="1"
                      max="3600"
                      step="1"
                      value={newIntervalDuration}
                      onChange={(e) => setNewIntervalDuration(Math.max(1, Number.parseInt(e.target.value) || 30))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="interval-type">{t.type}</Label>
                    <Select
                      value={newIntervalType}
                      onValueChange={(value) => setNewIntervalType(value as "work" | "rest")}
                    >
                      <SelectTrigger id="interval-type">
                        <SelectValue placeholder={t.selectType || "Select type..."} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="work">{t.work}</SelectItem>
                        <SelectItem value="rest">{t.rest}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                 <Button onClick={addInterval} disabled={newIntervalName.trim() === ""} className="w-full md:w-auto">
                  <Plus className="h-4 w-4 mr-2" />
                  {t.addInterval}
                </Button>
              </div>

              {/* Section Sauvegarde Template */}
              <div className="border-t pt-6">
                 <h4 className="font-medium mb-4">{t.saveCurrentListAsTemplate || "Save Current List as Template"}</h4>
                <div className="flex flex-col sm:flex-row items-stretch gap-4">
                  <Input
                    placeholder={t.templateName}
                    value={newTemplateName}
                    onChange={(e) => setNewTemplateName(e.target.value)}
                    className="flex-grow"
                    aria-label={t.templateName}
                  />
                  <Button
                    variant="outline"
                    onClick={saveTemplate}
                    disabled={newTemplateName.trim() === "" || intervals.length === 0}
                    className="flex-shrink-0"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {t.saveAsTemplate || "Save as Template"}
                  </Button>
                </div>
                 <p className="text-sm text-muted-foreground mt-2">
                    {t.saveTemplateHint || "Save the current list of intervals above as a reusable template."}
                </p>
              </div>
            </div>
          </TabsContent>

          {/* Onglet Templates */}
          <TabsContent value="templates">
            <div className="space-y-6">
              <h3 className="font-medium mb-4">{t.manageTemplates || "Manage Templates"}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {templates.length === 0 ? (
                  <div className="col-span-1 md:col-span-2 text-center py-8 text-muted-foreground border rounded-md">
                    {t.noTemplatesYet}
                  </div>
                ) : (
                  templates.map((template) => (
                    <Card key={template.id} className="overflow-hidden flex flex-col">
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-start gap-2">
                          {/* Infos Template */}
                          <div className="flex-grow min-w-0">
                            <CardTitle className="text-lg truncate" title={template.name}>{template.name}</CardTitle>
                            <CardDescription className="text-xs">
                              {template.intervals.length} {template.intervals.length === 1 ? t.interval : t.intervals},{" "}
                              ~{Math.ceil(template.intervals.reduce((total, interval) => total + interval.duration, 0) / 60)} min
                              {template.lastUsed && (
                                <span className="block mt-1">
                                  {t.lastUsed || "Last used"}: {new Date(template.lastUsed).toLocaleDateString()}
                                </span>
                              )}
                            </CardDescription>
                          </div>
                          {/* Actions Template */}
                          <div className="flex gap-1 flex-shrink-0">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => duplicateTemplate(template.id)}
                              title={t.duplicateTemplate || "Duplicate"}
                            >
                              <Copy className="h-4 w-4" />
                              <span className="sr-only">{t.duplicateTemplate}</span>
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-destructive hover:bg-destructive/10 hover:text-destructive"
                              onClick={(e) => {
                                  e.stopPropagation();
                                  const confirmMsg = formatTranslation(
                                    t.confirmDeleteTemplate || 'Are you sure you want to delete "{name}"?',
                                    { name: template.name },
                                  );
                                  if (window.confirm(confirmMsg)) {
                                    deleteTemplate(template.id);
                                  }
                                }}
                              title={t.deleteTemplate || "Delete"}
                            >
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">{t.deleteTemplate}</span>
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="flex-grow flex flex-col justify-between pt-2">
                         {/* Aperçu Badges Intervalles */}
                        <div className="flex flex-wrap gap-1 mb-4 max-h-16 overflow-y-auto text-xs">
                          {template.intervals.slice(0, 10).map((interval, idx) => (
                            <Badge
                              key={`${template.id}-${interval.id}-${idx}`} // Clé plus unique
                              variant="secondary"
                              className={`font-normal ${
                                interval.type === "work"
                                  ? "bg-red-100/70 dark:bg-red-900/50"
                                  : "bg-green-100/70 dark:bg-green-900/50"
                              }`}
                            >
                              {interval.name.substring(0,15)}{idx === 9 && template.intervals.length > 10 ? '...' : ''}
                            </Badge>
                          ))}
                           {template.intervals.length === 0 && <span className="text-muted-foreground italic">{t.noIntervals}</span>}
                        </div>
                         {/* Bouton Charger */}
                        <Button
                          onClick={() => {
                              setActiveTab('workout'); // Aller à l'onglet workout
                              loadTemplate(template.id); // Charger et reset
                          }}
                          className="w-full mt-auto"
                          variant={selectedTemplate === template.id ? "default" : "outline"}
                          size="sm"
                        >
                          {selectedTemplate === template.id
                            ? t.currentlySelected || "Selected"
                            : t.loadTemplate || "Load"}
                        </Button>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
  )

  return (
    <>
      {/* Wake Lock Manager - keeps screen awake during workout */}
      <WakeLockManager
        isActive={isRunning}
        onWakeLockChange={(isLocked) => {
          console.log(`[Workout Intervals Wake Lock] Status changed: ${isLocked ? 'Active' : 'Inactive'}`);
        }}
      />

      <Card className="w-full max-w-5xl mx-auto">
        <CardHeader>
          <div className="flex justify-between items-center flex-wrap gap-2">
            <div className="flex items-center gap-2">
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                {t.workoutIntervals}
              </CardTitle>
              {selectedTemplate && templates.find((t) => t.id === selectedTemplate) && (
                <Badge variant="outline" className="text-sm whitespace-nowrap">
                  {templates.find((t) => t.id === selectedTemplate)?.name}
                </Badge>
              )}
            </div>
            {/* Contrôles d'outils en haut à droite */}
            <ToolControls
              onFullscreenToggle={setIsToolFullscreen}
              variant="compact"
            />
          </div>
        </CardHeader>
        {mainContent}
      </Card>

      {/* Wrapper plein écran */}
      <ToolFullscreenWrapper
        isFullscreen={isToolFullscreen}
        onClose={() => setIsToolFullscreen(false)}
        toolName={t.workoutIntervals}
      >
        <Card className="w-full border-0 shadow-none bg-transparent">
          {mainContent}
        </Card>
      </ToolFullscreenWrapper>
    </>
  )
}
