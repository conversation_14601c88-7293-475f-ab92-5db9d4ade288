"use client"

import { useState, useEffect, useR<PERSON>, useCallback } from "react" // Added useCallback
import { motion } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { getTranslations } from "@/lib/i18n/translations"
import { Play, Pause, RefreshCw, Plus, Trash2, Save, Clock, Copy, Edit } from "lucide-react"
import { useSound } from "@/components/sound-provider"
import { Badge } from "@/components/ui/badge"
import { useRTL } from "@/hooks/useRTL"
import { useLanguage } from "@/components/language-provider"
import { ToolControls } from "@/components/tool-controls"
import { ToolFullscreen<PERSON>rapper } from "@/components/tool-fullscreen-wrapper"
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useNotificationPermission, useEnhancedAudioContext } from "@/components/wake-lock-manager"

interface IntervalsProps {
  lang: string
}

interface IntervalTime {
  hours: number
  minutes: number
  seconds: number
}

interface Interval {
  id: string
  name: string
  time: IntervalTime
  completed: boolean
}

interface IntervalSet {
  id: string
  name: string
  intervals: Interval[]
  createdAt: string
  lastUsed?: string
}

// --- Utility Functions ---
const timeToSeconds = (time: IntervalTime): number => {
  return (time?.hours || 0) * 3600 + (time?.minutes || 0) * 60 + (time?.seconds || 0);
};

const secondsToTime = (seconds: number): IntervalTime => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60); // Use floor for integer seconds
  return { hours, minutes, seconds: secs };
};

const formatTime = (time: IntervalTime): string => {
  return `${(time?.hours || 0).toString().padStart(2, "0")}:${(time?.minutes || 0).toString().padStart(2, "0")}:${(time?.seconds || 0).toString().padStart(2, "0")}`;
};

const formatSeconds = (seconds: number): string => {
  const time = secondsToTime(seconds);
  return formatTime(time);
};

// --- Storage Keys ---
const STORAGE_KEYS = {
  ACTIVE_INTERVALS: "timetools_active_intervals",
  INTERVAL_SETS: "timetools_interval_sets",
};

// --- LocalStorage Wrappers ---
function saveToLocalStorage<T>(key: string, data: T): void {
  try {
    if (typeof window !== "undefined") {
      const serializedData = JSON.stringify(data);
      localStorage.setItem(key, serializedData);
    }
  } catch (error) {
    console.error("Error saving to localStorage:", error);
  }
}

function getFromLocalStorage<T>(key: string, defaultValue: T): T {
  try {
    if (typeof window !== "undefined") {
      const serializedData = localStorage.getItem(key);
      if (serializedData === null) {
        return defaultValue;
      }
      return JSON.parse(serializedData) as T;
    }
    return defaultValue;
  } catch (error) {
    console.error("Error getting from localStorage:", error);
    return defaultValue;
  }
}

// --- Component ---
export function Intervals({ lang }: IntervalsProps) {
  const t = getTranslations(lang);
  const { language } = useLanguage();
  const isRTL = useRTL(language || lang);
  const [isRunning, setIsRunning] = useState(false);
  // State for remaining time in the current interval (in milliseconds)
  const [remainingTimeMs, setRemainingTimeMs] = useState(0);
  const [currentIntervalIndex, setCurrentIntervalIndex] = useState(0);
  const [intervals, setIntervals] = useState<Interval[]>([]); // Start empty, load from LS
  const [newIntervalName, setNewIntervalName] = useState("");
  const [newIntervalHours, setNewIntervalHours] = useState(0);
  const [newIntervalMinutes, setNewIntervalMinutes] = useState(5);
  const [newIntervalSeconds, setNewIntervalSeconds] = useState(0);
  const [savedIntervalSets, setSavedIntervalSets] = useState<IntervalSet[]>([]);
  const [newSetName, setNewSetName] = useState("");
  const [activeTab, setActiveTab] = useState("timer");
  const [editingSet, setEditingSet] = useState<IntervalSet | null>(null);
  const [isLoading, setIsLoading] = useState(true); // Loading state
  const originalTitle = useRef<string>("");
  const [isToolFullscreen, setIsToolFullscreen] = useState(false);

  // --- Refs ---
  const workerRef = useRef<Worker | null>(null);
  const { playSound } = useSound();
  // Ref to store remaining time when paused
  const pausedRemainingTimeRef = useRef<number | null>(null);
  // Ref to track if the component is mounted
  const isMountedRef = useRef(true);

  // Enhanced notification and wake lock management
  const {
    permission: notificationPermission,
    sendNotificationToServiceWorker
  } = useNotificationPermission()

  const {
    audioContext,
    initializeAudioContext,
    resumeAudioContext
  } = useEnhancedAudioContext()

  // Sauvegarder le titre original de la page au chargement du composant
  useEffect(() => {
    originalTitle.current = document.title;
    return () => {
      document.title = originalTitle.current;
    };
  }, []);

  // Initialize enhanced audio context on component mount
  useEffect(() => {
    initializeAudioContext()
  }, [initializeAudioContext])

  // Mettre à jour le titre de l'onglet du navigateur avec l'intervalle actuel
  useEffect(() => {
    // Utiliser requestAnimationFrame pour éviter les conflits avec d'autres mises à jour d'état
    const updateTitle = () => {
      if (isRunning && intervals.length > 0 && intervals[currentIntervalIndex]) {
        const formattedTime = formatSeconds(Math.max(0, Math.ceil(remainingTimeMs / 1000)));
        const newTitle = `${formattedTime} - ${intervals[currentIntervalIndex].name} - ${t.intervals || "Intervalles"}`;
        if (document.title !== newTitle) {
          document.title = newTitle;
          console.log(`Updated tab title: ${newTitle}`);
        }
      } else if (document.title !== originalTitle.current) {
        document.title = originalTitle.current;
        console.log(`Reset tab title to: ${originalTitle.current}`);
      }
    };

    // Utiliser un timeout pour éviter trop de mises à jour
    const timeoutId = setTimeout(updateTitle, 50);
    return () => clearTimeout(timeoutId);
  }, [remainingTimeMs, isRunning, intervals, currentIntervalIndex, t, originalTitle]);

  // --- Worker Initialization and Message Handling ---
  useEffect(() => {
    isMountedRef.current = true; // Mark as mounted
    workerRef.current = new Worker('/workers/countdown-worker.js');
    console.log("Countdown Worker initialized for Intervals.");

    // Cleanup: Terminate worker and mark as unmounted
    return () => {
      console.log("Terminating Countdown Worker for Intervals.");
      workerRef.current?.terminate();
      workerRef.current = null;
      isMountedRef.current = false; // Mark as unmounted
    };
  }, []); // Run only once on mount

  // Separate effect for message handling to avoid recreating the worker
  useEffect(() => {
    if (!workerRef.current || !isMountedRef.current) return;

    const handleWorkerMessage = (event: MessageEvent) => {
      // Ensure component is still mounted before updating state
      if (!isMountedRef.current) return;

      const { type, timeLeft } = event.data;

      if (type === 'tick') {
        setRemainingTimeMs(timeLeft); // Update remaining time state

        if (timeLeft <= 0) {
          // --- Interval Finished ---
          console.log(`Interval ${currentIntervalIndex} finished.`);

          // Enhanced audio handling with context resumption
          try {
            // Try to resume audio context if suspended
            if (audioContext && audioContext.state === "suspended") {
              resumeAudioContext().then(() => {
                playSound("bell");
                console.log("Interval finished, audio context resumed and sound played.");
              }).catch((error) => {
                console.warn("Could not resume audio context:", error);
                playSound("bell"); // Try anyway
              });
            } else {
              playSound("bell");
              console.log("Interval finished, playing sound.");
            }
          } catch (error) {
            console.warn("Could not play interval sound:", error);
          }

          // Enhanced notification handling
          const currentInterval = intervals[currentIntervalIndex];
          if (currentInterval && notificationPermission === "granted") {
            const notificationTitle = `${t.intervalFinished || "Intervalle terminé"}: ${currentInterval.name}`;
            const nextIndex = (currentIntervalIndex + 1) % intervals.length;
            const nextInterval = intervals[nextIndex];
            const notificationBody = nextInterval
              ? `${t.nextInterval || "Prochain"}: ${nextInterval.name}`
              : t.allIntervalsCompleted || "Tous les intervalles terminés";

            try {
              sendNotificationToServiceWorker(
                notificationTitle,
                notificationBody,
                "/icons/icon-192x192.png",
                "interval-finished",
                false // Don't require interaction for intervals
              );
              console.log("Interval notification sent to service worker.");
            } catch (error) {
              console.error("Error sending interval notification:", error);
            }
          }

          // Use functional updates for state changes based on previous state
          setIntervals(prevIntervals => {
            const completedIntervalId = prevIntervals[currentIntervalIndex]?.id;
            // Mark the just finished interval as completed
            const updatedIntervals = prevIntervals.map((int, index) =>
              index === currentIntervalIndex ? { ...int, completed: true } : int
            );

            // Determine the next index
            const nextIndex = (currentIntervalIndex + 1) % updatedIntervals.length;

            // If looping back to the start, reset all completion statuses
            if (nextIndex === 0) {
              console.log("Looping back to start, resetting completion.");
              return updatedIntervals.map(int => ({ ...int, completed: false }));
            }
            return updatedIntervals;
          });

          // Utiliser une fonction pour gérer la transition vers le prochain intervalle
          // pour éviter les problèmes de fermeture (closure)
          const moveToNextInterval = () => {
            const nextIndex = (currentIntervalIndex + 1) % intervals.length;
            console.log(`Moving to next interval: ${nextIndex}`);

            const nextInterval = intervals[nextIndex];
            if (!nextInterval) {
              console.error("Could not find next interval, stopping.");
              setIsRunning(false);
              return;
            }

            const nextIntervalDurationMs = timeToSeconds(nextInterval.time) * 1000;
            console.log(`Next interval duration: ${nextIntervalDurationMs}ms`);

            // Mettre à jour l'index avant de démarrer le worker
            setCurrentIntervalIndex(nextIndex);

            // Réinitialiser le temps restant pour le nouvel intervalle
            setRemainingTimeMs(nextIntervalDurationMs);
            pausedRemainingTimeRef.current = null;

            // Démarrer le worker pour le prochain intervalle
            const nextEndTime = Date.now() + nextIntervalDurationMs;
            if (workerRef.current) {
              workerRef.current.postMessage({ command: 'start', value: nextEndTime });
              console.log(`Started worker for interval ${nextIndex} with end time: ${new Date(nextEndTime).toLocaleTimeString()}`);
            }
          };

          // Exécuter la transition après un court délai pour s'assurer que les états sont mis à jour
          setTimeout(moveToNextInterval, 50);
        }
      } else if (type === 'stopped') {
        // Optional: Handle confirmation that worker stopped if needed
        console.log("Worker confirmed stopped.");
      }
    };

    // Attach the message handler
    workerRef.current.onmessage = handleWorkerMessage;

    // No cleanup needed here as the worker is managed in the previous effect
  }, [currentIntervalIndex, intervals, playSound]); // Dependencies for the message handler

  // --- Load Data from localStorage on Mount ---
  useEffect(() => {
    setIsLoading(true);
    // Load interval sets
    const savedSets = getFromLocalStorage<IntervalSet[]>(STORAGE_KEYS.INTERVAL_SETS, []);
    setSavedIntervalSets(savedSets);

    // Load active intervals
    const activeIntervals = getFromLocalStorage<Interval[]>(STORAGE_KEYS.ACTIVE_INTERVALS, []);
    if (activeIntervals.length > 0) {
      setIntervals(activeIntervals);
      // Initialize remaining time based on the first interval
      const firstIntervalDurationMs = timeToSeconds(activeIntervals[0].time) * 1000;
      setRemainingTimeMs(firstIntervalDurationMs);
    } else {
      // Set default intervals if none are loaded
      const defaultIntervals = [
        { id: "1", name: t.work, time: { hours: 0, minutes: 25, seconds: 0 }, completed: false },
        { id: "2", name: t.rest, time: { hours: 0, minutes: 5, seconds: 0 }, completed: false },
      ];
      setIntervals(defaultIntervals);
      const firstDefaultDurationMs = timeToSeconds(defaultIntervals[0].time) * 1000;
      setRemainingTimeMs(firstDefaultDurationMs);
    }
    setCurrentIntervalIndex(0); // Ensure index is reset
    setIsRunning(false); // Ensure timer is not running initially
    pausedRemainingTimeRef.current = null; // Reset pause state
    setIsLoading(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Run only once on mount

  // --- Save Active Intervals to localStorage ---
  useEffect(() => {
    // Don't save during initial load or if intervals are empty
    if (!isLoading && intervals.length > 0) {
      saveToLocalStorage(STORAGE_KEYS.ACTIVE_INTERVALS, intervals);
    }
  }, [intervals, isLoading]);

  // --- Calculate Total Time ---
  const getTotalTime = useCallback((): number => {
    return intervals.reduce((total, interval) => total + timeToSeconds(interval.time), 0);
  }, [intervals]);

  // --- Timer Control Functions ---
  const toggleTimer = useCallback(() => {
    if (intervals.length === 0 || !workerRef.current || !isMountedRef.current) return;

    // Utiliser une fonction pour mettre à jour l'état isRunning et éviter les problèmes de fermeture
    const updateRunningState = (nextRunning: boolean) => {
      console.log(`Setting isRunning to ${nextRunning}`);
      setIsRunning(nextRunning);

      if (nextRunning) {
        // --- Start / Resume ---
        let shouldResetCompletion = false;
        // Determine if we need to reset completion (starting from the beginning)
        if (currentIntervalIndex === 0) {
          const firstIntervalDurationMs = timeToSeconds(intervals[0]?.time || {hours:0,minutes:0,seconds:0}) * 1000;
          // If no pause time or pause time equals full duration of first item
          if (pausedRemainingTimeRef.current === null || pausedRemainingTimeRef.current >= firstIntervalDurationMs) {
            shouldResetCompletion = true;
          }
        }

        if (shouldResetCompletion) {
          console.log("Starting from beginning, resetting completion states.");
          setIntervals(prev => prev.map(int => ({ ...int, completed: false })));
        }

        const currentInterval = intervals[currentIntervalIndex];
        if (!currentInterval) {
          console.error("No current interval found, cannot start timer.");
          setIsRunning(false);
          return;
        }

        // Use saved remaining time if resuming, otherwise use the current remainingTimeMs state
        const durationToStartMs = pausedRemainingTimeRef.current ?? remainingTimeMs;
        console.log(`Starting with duration: ${durationToStartMs}ms (${durationToStartMs/1000}s)`);

        if (durationToStartMs <= 0) {
          console.warn("Attempting to start timer with zero or negative duration.");
          setIsRunning(false); // Don't start
          return;
        }

        const endTime = Date.now() + durationToStartMs;
        // Set remaining time state just before starting worker (visual consistency)
        setRemainingTimeMs(durationToStartMs);

        // Démarrer le worker avec un court délai pour s'assurer que les états sont mis à jour
        setTimeout(() => {
          if (workerRef.current) {
            workerRef.current.postMessage({ command: 'start', value: endTime });
            console.log(`Timer started/resumed for interval ${currentIntervalIndex}. End time: ${new Date(endTime).toLocaleTimeString()}, Duration: ${durationToStartMs}ms`);
          }
        }, 50);

        pausedRemainingTimeRef.current = null; // Clear saved pause time

      } else {
        // --- Pause ---
        if (workerRef.current) {
          workerRef.current.postMessage({ command: 'stop' });
          // Save the *current* remaining time from state when pausing
          pausedRemainingTimeRef.current = remainingTimeMs;
          console.log(`Timer paused at interval ${currentIntervalIndex}. Remaining: ${remainingTimeMs}ms (${remainingTimeMs/1000}s)`);
        }
      }
    };

    // Appeler la fonction avec l'état inverse de isRunning
    updateRunningState(!isRunning);

  }, [isRunning, intervals, currentIntervalIndex, remainingTimeMs]); // Added remainingTimeMs dependency

  const resetTimer = useCallback(() => {
    if (workerRef.current) {
      workerRef.current.postMessage({ command: 'stop' });
    }
    setIsRunning(false);
    setCurrentIntervalIndex(0);
    pausedRemainingTimeRef.current = null;

    // Reset displayed time to the duration of the *first* interval
    if (intervals.length > 0 && intervals[0]) {
      const firstIntervalDurationMs = timeToSeconds(intervals[0].time) * 1000;
      setRemainingTimeMs(firstIntervalDurationMs);
    } else {
      setRemainingTimeMs(0);
    }

    // Reset completion status for all intervals
    setIntervals(prevIntervals => prevIntervals.map(int => ({ ...int, completed: false })));

    // Restaurer le titre original
    document.title = originalTitle.current;

    console.log("Timer reset.");
  }, [intervals]); // Depends on intervals to get the first item's duration

  // --- Interval Management Functions ---
  const addInterval = useCallback(() => {
    if (newIntervalName.trim() === "") return;
    const durationSeconds = timeToSeconds({
        hours: newIntervalHours,
        minutes: newIntervalMinutes,
        seconds: newIntervalSeconds,
    });
    if (durationSeconds <= 0) return; // Don't add zero-duration intervals

    const newInterval: Interval = {
      id: Date.now().toString(),
      name: newIntervalName,
      time: { hours: newIntervalHours, minutes: newIntervalMinutes, seconds: newIntervalSeconds },
      completed: false,
    };

    setIntervals(prev => [...prev, newInterval]);
    // Reset form
    setNewIntervalName("");
    setNewIntervalHours(0);
    setNewIntervalMinutes(5);
    setNewIntervalSeconds(0);
  }, [newIntervalName, newIntervalHours, newIntervalMinutes, newIntervalSeconds]);

  const removeInterval = useCallback((id: string) => {
    // Prevent removing the last interval if editing is not allowed or desired
    if (intervals.length <= 1) {
        console.warn("Cannot remove the last interval.");
        return; // Or allow removal and handle empty state
    }

    const intervalToRemoveIndex = intervals.findIndex(int => int.id === id);
    if (intervalToRemoveIndex === -1) return;

    const newIntervals = intervals.filter((interval) => interval.id !== id);

    // Stop the timer and reset state if running
    if (isRunning) {
        resetTimer(); // Reset stops the worker and resets index/time/completion
        // Update intervals *after* reset to avoid state conflicts
        setIntervals(newIntervals);
    } else {
        // If stopped, update intervals and adjust index/time if needed
        setIntervals(newIntervals);
        // If the removed item was before or at the current index, adjust index
        let newIndex = currentIntervalIndex;
        if (intervalToRemoveIndex < currentIntervalIndex) {
            newIndex = currentIntervalIndex - 1;
        } else if (intervalToRemoveIndex === currentIntervalIndex) {
            // If removing the current one, reset to index 0 or the new last index if 0 is invalid
            newIndex = 0; // Simplest approach: reset to start
        }
        // Ensure index is valid
        newIndex = Math.max(0, Math.min(newIndex, newIntervals.length - 1));

        setCurrentIntervalIndex(newIndex);
        // Update remaining time display based on the new current interval
        if (newIntervals.length > 0 && newIntervals[newIndex]) {
            setRemainingTimeMs(timeToSeconds(newIntervals[newIndex].time) * 1000);
        } else {
            setRemainingTimeMs(0); // Handle empty intervals case
        }
        pausedRemainingTimeRef.current = null; // Reset pause state
    }
  }, [intervals, isRunning, currentIntervalIndex, resetTimer]);

  // --- Interval Set Management ---
  const saveIntervalSet = useCallback(() => {
    if (newSetName.trim() === "" || intervals.length === 0) return;

    const newSet: IntervalSet = {
      id: Date.now().toString(),
      name: newSetName,
      intervals: intervals.map(int => ({ ...int, time: { ...int.time } })), // Deep copy
      createdAt: new Date().toISOString(),
    };

    setSavedIntervalSets(prev => {
      const updatedSets = [...prev, newSet];
      saveToLocalStorage(STORAGE_KEYS.INTERVAL_SETS, updatedSets);
      return updatedSets;
    });
    setNewSetName(""); // Reset form field
  }, [newSetName, intervals]);

  const updateIntervalSet = useCallback(() => {
    if (!editingSet) return;

    const updatedSetData: IntervalSet = {
      ...editingSet,
      intervals: intervals.map(int => ({ ...int, time: { ...int.time } })), // Deep copy
      lastUsed: new Date().toISOString(), // Update last used timestamp
    };

    setSavedIntervalSets(prev => {
      const updatedSets = prev.map((set) => (set.id === editingSet.id ? updatedSetData : set));
      saveToLocalStorage(STORAGE_KEYS.INTERVAL_SETS, updatedSets);
      return updatedSets;
    });
    setEditingSet(null); // Exit editing mode
    // Optionally switch tab or provide feedback
  }, [editingSet, intervals]);

  const loadIntervalSet = useCallback((setId: string) => {
    const setToLoad = savedIntervalSets.find((s) => s.id === setId);
    if (!setToLoad) return;

    // Update lastUsed timestamp
    setSavedIntervalSets(prev => {
        const updatedSets = prev.map((s) =>
            s.id === setId ? { ...s, lastUsed: new Date().toISOString() } : s
        );
        saveToLocalStorage(STORAGE_KEYS.INTERVAL_SETS, updatedSets);
        return updatedSets;
    });

    // Deep copy intervals and reset completion
    const intervalsCopy = setToLoad.intervals.map((interval) => ({
      ...interval,
      completed: false,
      time: { ...interval.time },
    }));

    // Stop current timer and reset state before loading new intervals
    resetTimer(); // This handles worker stop, state reset

    // Set the new intervals AFTER resetting
    setIntervals(intervalsCopy);

    // Update remaining time display for the first interval of the loaded set
    if (intervalsCopy.length > 0 && intervalsCopy[0]) {
        setRemainingTimeMs(timeToSeconds(intervalsCopy[0].time) * 1000);
    } else {
        setRemainingTimeMs(0);
    }

    setEditingSet(null); // Ensure not in editing mode
    setActiveTab("timer"); // Switch to timer tab
  }, [savedIntervalSets, resetTimer]);

  const editSet = useCallback((setId: string) => {
    const setToEdit = savedIntervalSets.find((s) => s.id === setId);
    if (!setToEdit) return;

    // Deep copy intervals
    const intervalsCopy = setToEdit.intervals.map(interval => ({
        ...interval,
        time: { ...interval.time }
    }));

    // Stop timer and reset state
    resetTimer();

    // Load intervals for editing
    setIntervals(intervalsCopy);
    setEditingSet(setToEdit); // Enter editing mode

     // Update remaining time display for the first interval
    if (intervalsCopy.length > 0 && intervalsCopy[0]) {
        setRemainingTimeMs(timeToSeconds(intervalsCopy[0].time) * 1000);
    } else {
        setRemainingTimeMs(0);
    }

    setActiveTab("timer"); // Switch to timer tab for editing
  }, [savedIntervalSets, resetTimer]);

  const duplicateIntervalSet = useCallback((setId: string) => {
    const setToDuplicate = savedIntervalSets.find((s) => s.id === setId);
    if (!setToDuplicate) return;

    const newSet: IntervalSet = {
      id: Date.now().toString(),
      name: `${setToDuplicate.name} (${t.copy || "Copy"})`,
      // Deep copy intervals
      intervals: setToDuplicate.intervals.map(int => ({ ...int, time: { ...int.time } })),
      createdAt: new Date().toISOString(),
    };

    setSavedIntervalSets(prev => {
        const updatedSets = [...prev, newSet];
        saveToLocalStorage(STORAGE_KEYS.INTERVAL_SETS, updatedSets);
        return updatedSets;
    });
  }, [savedIntervalSets, t.copy]);

  const deleteIntervalSet = useCallback((setId: string) => {
    // Add confirmation dialog
    if (!confirm(`${t.confirmDeleteSet || "Are you sure you want to delete this set?"}`)) {
        return;
    }

    setSavedIntervalSets(prev => {
        const updatedSets = prev.filter((s) => s.id !== setId);
        saveToLocalStorage(STORAGE_KEYS.INTERVAL_SETS, updatedSets);
        return updatedSets;
    });

    // If the deleted set was being edited, exit editing mode
    if (editingSet && editingSet.id === setId) {
      setEditingSet(null);
      // Optionally reset the intervals list to default or the first saved set
      // resetTimer(); // Reset timer state as well
    }
  }, [editingSet, t.confirmDeleteSet]);

  // --- Calculations for UI ---
  const currentInterval = intervals[currentIntervalIndex];
  const currentIntervalDurationSeconds = currentInterval ? timeToSeconds(currentInterval.time) : 0;
  // Calculate progress based on remaining time
  const progress = currentIntervalDurationSeconds > 0
    ? Math.max(0, 100 - (remainingTimeMs / (currentIntervalDurationSeconds * 1000)) * 100)
    : 0;

  // --- Render ---
  if (isLoading) {
      return (
          <Card className="w-full max-w-4xl mx-auto">
              <CardHeader><CardTitle>{t.loading || "Loading..."}</CardTitle></CardHeader>
              <CardContent><div className="text-center p-8">...</div></CardContent>
          </Card>
      );
  }

  const intervalsContent = (
    <>
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2 w-full mb-6">
          <TabsTrigger value="timer" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            {t.timer || "Timer"}
          </TabsTrigger>
          <TabsTrigger value="sets" className="flex items-center gap-2">
            <Save className="h-4 w-4" />
            {t.intervalSets || "Interval Sets"}
          </TabsTrigger>
        </TabsList>

        {/* === Timer Tab === */}
        <TabsContent value="timer">
          {intervals.length > 0 && currentInterval ? (
            <div className="mb-8">
              {/* Interval Name and Index */}
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-xl font-bold truncate" title={currentInterval.name}>
                    {currentInterval.name}
                </h3>
                <p className="text-sm text-muted-foreground flex-shrink-0 ml-2">
                  {currentIntervalIndex + 1} / {intervals.length}
                </p>
              </div>

              {/* Progress Bar */}
              <div className="w-full h-3 bg-muted rounded-full mb-8 overflow-hidden">
                <motion.div
                  className="h-full bg-primary"
                  style={{ width: `${progress}%` }}
                  animate={{ width: `${progress}%` }}
                  transition={{ duration: 0.15, ease: "linear" }} // Smooth transition
                />
              </div>

              {/* Remaining Time */}
              <div className="text-6xl md:text-7xl font-mono font-bold text-center mb-10">
                {/* Display remaining time, ensuring it doesn't go below zero */}
                {formatSeconds(Math.max(0, Math.ceil(remainingTimeMs / 1000)))}
              </div>

              {/* Controls */}
              <div className="flex justify-center gap-6">
                <Button
                  onClick={toggleTimer}
                  size="lg"
                  className="w-20 h-20 rounded-full"
                  variant={isRunning ? "destructive" : "default"}
                  disabled={intervals.length === 0}
                  aria-label={isRunning ? t.pause : t.start}
                >
                  {isRunning ? <Pause className="h-8 w-8" /> : <Play className="h-8 w-8" />}
                </Button>
                <Button
                  onClick={resetTimer}
                  size="lg"
                  variant="outline"
                  className="w-18 h-18 rounded-full"
                  disabled={intervals.length === 0 || (!isRunning && currentIntervalIndex === 0 && remainingTimeMs >= currentIntervalDurationSeconds * 1000)} // Disable if already reset
                  aria-label={t.reset}
                >
                  <RefreshCw className="h-7 w-7" />
                </Button>
              </div>

              {/* Total Duration */}
              <div className="mt-4 text-center">
                <p className="text-sm text-muted-foreground">
                  {t.totalDuration}: {formatSeconds(getTotalTime())}
                </p>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground mb-8">{t.noIntervalsDefinedYet}</div>
          )}

          {/* --- Interval List and Add Form --- */}
          <div className="border-t pt-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-medium">{t.intervals}</h3>
              {/* Show Update button only when editing */}
              {editingSet && (
                <Button variant="outline" size="sm" onClick={updateIntervalSet} className="flex items-center" dir={isRTL ? "rtl" : "ltr"}>
                  <Save className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                  {t.updateSet || "Update Set"}
                </Button>
              )}
            </div>

            {/* Interval List */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              {intervals.map((interval, index) => (
                <div
                  key={interval.id}
                  className={`flex justify-between items-center p-3 border rounded-md transition-colors ${
                    index === currentIntervalIndex && isRunning ? 'border-primary ring-1 ring-primary' : ''
                  } ${interval.completed ? 'opacity-50' : ''}`}
                >
                  <div>
                    <div className="flex items-center gap-2">
                      <Badge variant={index === currentIntervalIndex && isRunning ? "default" : "outline"} className="text-xs">
                        {index + 1}
                      </Badge>
                      <p className={`font-medium truncate ${interval.completed ? 'line-through' : ''}`} title={interval.name}>
                          {interval.name}
                      </p>
                    </div>
                    <p className="text-sm text-muted-foreground">{formatTime(interval.time)}</p>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => removeInterval(interval.id)}
                    className="h-8 w-8 text-destructive flex-shrink-0"
                    disabled={intervals.length <= 1} // Prevent removing the last interval
                    aria-label={t.delete}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>

            {/* Add Interval Form */}
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4" dir={isRTL ? "rtl" : "ltr"}>
                <div>
                  <Label htmlFor="interval-name">{t.name}</Label>
                  <Input
                    id="interval-name"
                    placeholder={t.intervalName}
                    value={newIntervalName}
                    onChange={(e) => setNewIntervalName(e.target.value)}
                    dir={isRTL ? "rtl" : "ltr"}
                  />
                </div>
                <div className="grid grid-cols-3 gap-2" dir={isRTL ? "rtl" : "ltr"}>
                  <div>
                    <Label htmlFor="interval-hours">{t.hours}</Label>
                    <Input
                      id="interval-hours" type="number" min="0" max="99" value={newIntervalHours}
                      onChange={(e) => setNewIntervalHours(Math.max(0, Math.min(99, Number.parseInt(e.target.value) || 0)))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="interval-minutes">{t.minutes}</Label>
                    <Input
                      id="interval-minutes" type="number" min="0" max="59" value={newIntervalMinutes}
                      onChange={(e) => setNewIntervalMinutes(Math.max(0, Math.min(59, Number.parseInt(e.target.value) || 0)))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="interval-seconds">{t.seconds}</Label>
                    <Input
                      id="interval-seconds" type="number" min="0" max="59" value={newIntervalSeconds}
                      onChange={(e) => setNewIntervalSeconds(Math.max(0, Math.min(59, Number.parseInt(e.target.value) || 0)))}
                    />
                  </div>
                </div>
              </div>
              <Button
                onClick={addInterval}
                disabled={newIntervalName.trim() === "" || (newIntervalHours === 0 && newIntervalMinutes === 0 && newIntervalSeconds === 0)}
                className="flex items-center"
                dir={isRTL ? "rtl" : "ltr"}
              >
                <Plus className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                {t.addInterval}
              </Button>

              {/* Save Set Form (only if not editing) */}
              {!editingSet && (
                <div className="border-t pt-4 mt-4">
                  <div dir={isRTL ? "rtl" : "ltr"}>
                    <Label htmlFor="set-name" className="mb-2 block">
                      {t.saveIntervalSet || "Save current intervals as set"}
                    </Label>
                    <div className="flex gap-2 items-center">
                      <div className="flex-1">
                        <Input
                          id="set-name"
                          placeholder={t.intervalSetName || "Set name"}
                          value={newSetName}
                          onChange={(e) => setNewSetName(e.target.value)}
                          dir={isRTL ? "rtl" : "ltr"}
                        />
                      </div>
                      <Button
                        variant="outline"
                        onClick={saveIntervalSet}
                        disabled={newSetName.trim() === "" || intervals.length === 0}
                        className="flex-shrink-0 flex items-center h-10"
                        dir={isRTL ? "rtl" : "ltr"}
                      >
                        <Save className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                        {t.save}
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </TabsContent>

        {/* === Sets Tab === */}
        <TabsContent value="sets">
          <div className="space-y-6">
            <h3 className="font-medium">{t.savedIntervalSets || "Saved Interval Sets"}</h3>
            {savedIntervalSets.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground border rounded-md">
                {t.noSavedSets || "No saved interval sets yet"}
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-4">
                {/* Sort sets by lastUsed (most recent first) or createdAt */}
                {savedIntervalSets
                  .sort((a, b) => (b.lastUsed ? new Date(b.lastUsed).getTime() : new Date(b.createdAt).getTime()) - (a.lastUsed ? new Date(a.lastUsed).getTime() : new Date(a.createdAt).getTime()))
                  .map((set) => (
                  <Card key={set.id} className="overflow-hidden">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start gap-2">
                        {/* Set Details */}
                        <div className="flex-1 overflow-hidden">
                          <CardTitle className="text-lg truncate" title={set.name}>{set.name}</CardTitle>
                          <CardDescription className="text-sm">
                            {set.intervals.length} {t.intervals.toLowerCase()},{" "}
                            {formatSeconds(set.intervals.reduce((total, interval) => total + timeToSeconds(interval.time), 0))}
                            {set.lastUsed && (
                              <span className="block mt-1 text-xs">
                                {t.lastUsed || "Last used"}: {new Date(set.lastUsed).toLocaleDateString()}
                              </span>
                            )}
                          </CardDescription>
                        </div>
                        {/* Action Buttons */}
                        <div className="flex gap-1 flex-shrink-0">
                          <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => editSet(set.id)} title={t.edit}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => duplicateIntervalSet(set.id)} title={t.duplicate || "Duplicate"}>
                            <Copy className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" className="h-8 w-8 text-destructive" onClick={() => deleteIntervalSet(set.id)} title={t.delete}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      {/* Interval Badges */}
                      <div className="flex flex-wrap gap-1 mb-4">
                        {set.intervals.slice(0, 10).map((interval, index) => ( // Limit badges shown
                          <Badge key={index} variant="secondary" className="text-xs">
                            {interval.name}
                          </Badge>
                        ))}
                        {set.intervals.length > 10 && <Badge variant="secondary">...</Badge>}
                      </div>
                      {/* Load Button */}
                      <Button onClick={() => loadIntervalSet(set.id)} className="w-full">
                        {t.load || "Load"}
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
            {/* "Create New Set" form moved to Timer tab for better UX */}
          </div>
        </TabsContent>
      </Tabs>
    </>
  )

  return (
    <>
      {/* Wake Lock Manager - keeps screen awake during intervals */}
      <WakeLockManager
        isActive={isRunning}
        onWakeLockChange={(isLocked) => {
          console.log(`[Intervals Wake Lock] Status changed: ${isLocked ? 'Active' : 'Inactive'}`);
        }}
      />

      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                {t.intervalsTitle}
              </CardTitle>
              {editingSet && (
                <CardDescription>
                  {t.editing || "Editing"}: {editingSet.name}
                </CardDescription>
              )}
            </div>
            <ToolControls
              onFullscreenToggle={setIsToolFullscreen}
              variant="compact"
            />
          </div>
        </CardHeader>
        <CardContent>
          {intervalsContent}
        </CardContent>
      </Card>

      {/* Wrapper plein écran */}
      <ToolFullscreenWrapper
        isFullscreen={isToolFullscreen}
        onClose={() => setIsToolFullscreen(false)}
        toolName={t.intervalsTitle}
      >
        <Card className="w-full border-0 shadow-none bg-transparent">
          <CardContent>
            {intervalsContent}
          </CardContent>
        </Card>
      </ToolFullscreenWrapper>
    </>
  );
}
