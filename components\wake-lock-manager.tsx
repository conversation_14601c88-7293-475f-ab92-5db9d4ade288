"use client"

import { useEffect, useRef, useState } from "react"

interface WakeLockManagerProps {
  isActive: boolean
  onWakeLockChange?: (isLocked: boolean) => void
}

export function WakeLockManager({ isActive, onWakeLockChange }: WakeLockManagerProps) {
  const wakeLockRef = useRef<WakeLockSentinel | null>(null)
  const [isWakeLockSupported, setIsWakeLockSupported] = useState(false)
  const [isWakeLockActive, setIsWakeLockActive] = useState(false)

  // Check if Wake Lock API is supported
  useEffect(() => {
    if (typeof window !== "undefined" && "navigator" in window && "wakeLock" in navigator) {
      setIsWakeLockSupported(true)
      console.log("[Wake Lock] Wake Lock API is supported")
    } else {
      console.log("[Wake Lock] Wake Lock API is not supported")
    }
  }, [])

  // Request wake lock when timer is active
  const requestWakeLock = async () => {
    if (!isWakeLockSupported || wakeLockRef.current) {
      return
    }

    try {
      wakeLockRef.current = await navigator.wakeLock.request("screen")
      setIsWakeLockActive(true)
      onWakeLockChange?.(true)
      
      console.log("[Wake Lock] Screen wake lock acquired")

      // Listen for wake lock release
      wakeLockRef.current.addEventListener("release", () => {
        console.log("[Wake Lock] Screen wake lock was released")
        setIsWakeLockActive(false)
        onWakeLockChange?.(false)
        wakeLockRef.current = null
      })
    } catch (error) {
      console.error("[Wake Lock] Failed to acquire screen wake lock:", error)
    }
  }

  // Release wake lock when timer is stopped
  const releaseWakeLock = async () => {
    if (wakeLockRef.current) {
      try {
        await wakeLockRef.current.release()
        wakeLockRef.current = null
        setIsWakeLockActive(false)
        onWakeLockChange?.(false)
        console.log("[Wake Lock] Screen wake lock released manually")
      } catch (error) {
        console.error("[Wake Lock] Failed to release screen wake lock:", error)
      }
    }
  }

  // Handle visibility change (re-acquire wake lock when page becomes visible)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible" && isActive && !wakeLockRef.current) {
        console.log("[Wake Lock] Page became visible, re-acquiring wake lock")
        requestWakeLock()
      }
    }

    document.addEventListener("visibilitychange", handleVisibilityChange)
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange)
    }
  }, [isActive])

  // Manage wake lock based on isActive prop
  useEffect(() => {
    if (isActive) {
      requestWakeLock()
    } else {
      releaseWakeLock()
    }
  }, [isActive])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (wakeLockRef.current) {
        wakeLockRef.current.release()
      }
    }
  }, [])

  // This component doesn't render anything visible
  return null
}

// Hook for managing notification permissions
export function useNotificationPermission() {
  const [permission, setPermission] = useState<NotificationPermission>("default")
  const [isSupported, setIsSupported] = useState(false)

  useEffect(() => {
    if (typeof window !== "undefined" && "Notification" in window) {
      setIsSupported(true)
      setPermission(Notification.permission)
      console.log("[Notifications] Notification API is supported, current permission:", Notification.permission)
    } else {
      console.log("[Notifications] Notification API is not supported")
    }
  }, [])

  const requestPermission = async (): Promise<NotificationPermission> => {
    if (!isSupported) {
      console.warn("[Notifications] Notification API not supported")
      return "denied"
    }

    try {
      const result = await Notification.requestPermission()
      setPermission(result)
      console.log("[Notifications] Permission request result:", result)
      return result
    } catch (error) {
      console.error("[Notifications] Failed to request notification permission:", error)
      return "denied"
    }
  }

  const sendNotificationToServiceWorker = (
    title: string,
    body: string,
    icon?: string,
    tag?: string,
    requireInteraction: boolean = true
  ) => {
    if (permission !== "granted") {
      console.warn("[Notifications] Permission not granted, cannot send notification")
      return
    }

    if ("serviceWorker" in navigator) {
      navigator.serviceWorker.ready.then((registration) => {
        if (registration.active) {
          registration.active.postMessage({
            type: "TIMER_FINISHED",
            title,
            body,
            icon,
            tag,
            requireInteraction
          })
        }
      })
    }
  }

  const scheduleNotificationInServiceWorker = (
    delay: number,
    title: string,
    body: string,
    icon?: string,
    tag?: string,
    requireInteraction: boolean = true
  ) => {
    if (permission !== "granted") {
      console.warn("[Notifications] Permission not granted, cannot schedule notification")
      return
    }

    if ("serviceWorker" in navigator) {
      navigator.serviceWorker.ready.then((registration) => {
        if (registration.active) {
          registration.active.postMessage({
            type: "SCHEDULE_TIMER_NOTIFICATION",
            delay,
            title,
            body,
            icon,
            tag,
            requireInteraction
          })
        }
      })
    }
  }

  return {
    permission,
    isSupported,
    requestPermission,
    sendNotificationToServiceWorker,
    scheduleNotificationInServiceWorker
  }
}

// Hook for enhanced audio context management with multiple fallbacks
export function useEnhancedAudioContext() {
  const audioContextRef = useRef<AudioContext | null>(null)
  const audioElementsRef = useRef<HTMLAudioElement[]>([])
  const [isAudioContextActive, setIsAudioContextActive] = useState(false)
  const [isAudioUnlocked, setIsAudioUnlocked] = useState(false)

  const initializeAudioContext = async () => {
    if (typeof window === "undefined" || audioContextRef.current) return

    try {
      const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext
      if (AudioContextClass) {
        audioContextRef.current = new AudioContextClass()

        // Resume audio context if it's suspended
        if (audioContextRef.current.state === "suspended") {
          await audioContextRef.current.resume()
        }

        setIsAudioContextActive(audioContextRef.current.state === "running")
        console.log("[Audio] AudioContext initialized, state:", audioContextRef.current.state)
      }
    } catch (error) {
      console.error("[Audio] Failed to initialize AudioContext:", error)
    }
  }

  const resumeAudioContext = async () => {
    if (audioContextRef.current && audioContextRef.current.state === "suspended") {
      try {
        await audioContextRef.current.resume()
        setIsAudioContextActive(true)
        console.log("[Audio] AudioContext resumed")
        return true
      } catch (error) {
        console.error("[Audio] Failed to resume AudioContext:", error)
        return false
      }
    }
    return audioContextRef.current?.state === "running"
  }

  // Create multiple pre-loaded audio elements for better reliability
  const preloadAudioElements = () => {
    if (typeof window === "undefined") return

    // Create multiple audio elements for redundancy
    for (let i = 0; i < 3; i++) {
      const audio = new Audio()
      audio.preload = "auto"
      audio.src = "/sound/bell.mp3"
      audio.volume = 0.8

      // Handle loading
      audio.addEventListener("canplaythrough", () => {
        console.log(`[Audio] Audio element ${i} preloaded successfully`)
      })

      audio.addEventListener("error", (e) => {
        console.warn(`[Audio] Audio element ${i} failed to load:`, e)
      })

      audioElementsRef.current.push(audio)
    }
  }

  // Enhanced audio unlock with multiple strategies
  const unlockAudio = async () => {
    if (isAudioUnlocked) return true

    try {
      // Strategy 1: Resume AudioContext
      if (audioContextRef.current) {
        await resumeAudioContext()
      }

      // Strategy 2: Play silent audio to unlock
      const testAudio = new Audio()
      testAudio.src = "data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT"
      testAudio.volume = 0.01

      const playPromise = testAudio.play()
      if (playPromise) {
        await playPromise
        testAudio.pause()
        testAudio.currentTime = 0
      }

      // Strategy 3: Test with actual audio elements
      if (audioElementsRef.current.length > 0) {
        const testElement = audioElementsRef.current[0]
        testElement.volume = 0.01
        const testPlay = testElement.play()
        if (testPlay) {
          await testPlay
          testElement.pause()
          testElement.currentTime = 0
          testElement.volume = 0.8
        }
      }

      setIsAudioUnlocked(true)
      console.log("[Audio] Audio successfully unlocked")
      return true
    } catch (error) {
      console.warn("[Audio] Failed to unlock audio:", error)
      return false
    }
  }

  // Enhanced play sound function with multiple fallbacks
  const playEnhancedSound = async (type: "bell" | "alarm" | "notification" = "bell") => {
    console.log(`[Audio] Attempting to play ${type} sound`)

    // Try to unlock audio first
    await unlockAudio()

    let soundPlayed = false

    // Strategy 1: Use pre-loaded audio elements (most reliable for PWA)
    if (audioElementsRef.current.length > 0 && !soundPlayed) {
      for (const audio of audioElementsRef.current) {
        try {
          if (audio.readyState >= 2) { // HAVE_CURRENT_DATA
            audio.currentTime = 0
            audio.volume = 0.8
            const playPromise = audio.play()
            if (playPromise) {
              await playPromise
              console.log("[Audio] Sound played via pre-loaded audio element")
              soundPlayed = true
              break
            }
          }
        } catch (error) {
          console.warn("[Audio] Failed to play via audio element:", error)
          continue
        }
      }
    }

    // Strategy 2: Create new audio element
    if (!soundPlayed) {
      try {
        const audio = new Audio("/sound/bell.mp3")
        audio.volume = 0.8
        const playPromise = audio.play()
        if (playPromise) {
          await playPromise
          console.log("[Audio] Sound played via new audio element")
          soundPlayed = true
        }
      } catch (error) {
        console.warn("[Audio] Failed to play via new audio element:", error)
      }
    }

    // Strategy 3: Use AudioContext with synthetic sound
    if (!soundPlayed && audioContextRef.current) {
      try {
        await resumeAudioContext()
        if (audioContextRef.current.state === "running") {
          generateSyntheticBell(audioContextRef.current)
          console.log("[Audio] Sound played via AudioContext")
          soundPlayed = true
        }
      } catch (error) {
        console.warn("[Audio] Failed to play via AudioContext:", error)
      }
    }

    if (!soundPlayed) {
      console.error("[Audio] All audio playback strategies failed")
    }

    return soundPlayed
  }

  // Generate synthetic bell sound
  const generateSyntheticBell = (ctx: AudioContext) => {
    const now = ctx.currentTime
    const oscillator = ctx.createOscillator()
    const gainNode = ctx.createGain()

    oscillator.type = "sine"
    oscillator.frequency.value = 800

    gainNode.gain.setValueAtTime(0, now)
    gainNode.gain.linearRampToValueAtTime(0.3, now + 0.02)
    gainNode.gain.linearRampToValueAtTime(0.2, now + 0.1)
    gainNode.gain.linearRampToValueAtTime(0, now + 0.5)

    oscillator.connect(gainNode)
    gainNode.connect(ctx.destination)

    oscillator.start(now)
    oscillator.stop(now + 0.5)

    setTimeout(() => {
      try {
        oscillator.disconnect()
        gainNode.disconnect()
      } catch (e) {
        // Ignore cleanup errors
      }
    }, 500)
  }

  // Handle user interaction to unlock audio
  useEffect(() => {
    const handleUserInteraction = () => {
      unlockAudio()
    }

    // Add event listeners for user interactions
    const events = ["click", "touchstart", "touchend", "keydown", "mousedown"]
    events.forEach(event => {
      document.addEventListener(event, handleUserInteraction, { once: true, passive: true })
    })

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleUserInteraction)
      })
    }
  }, [isAudioUnlocked])

  // Initialize on mount
  useEffect(() => {
    initializeAudioContext()
    preloadAudioElements()
  }, [])

  return {
    audioContext: audioContextRef.current,
    isAudioContextActive,
    isAudioUnlocked,
    initializeAudioContext,
    resumeAudioContext,
    playEnhancedSound
  }
}
