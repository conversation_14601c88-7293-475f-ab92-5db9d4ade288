"use client"

import { useEffect, useRef, useState } from "react"

interface WakeLockManagerProps {
  isActive: boolean
  onWakeLockChange?: (isLocked: boolean) => void
}

export function WakeLockManager({ isActive, onWakeLockChange }: WakeLockManagerProps) {
  const wakeLockRef = useRef<WakeLockSentinel | null>(null)
  const [isWakeLockSupported, setIsWakeLockSupported] = useState(false)
  const [isWakeLockActive, setIsWakeLockActive] = useState(false)

  // Check if Wake Lock API is supported
  useEffect(() => {
    if (typeof window !== "undefined" && "navigator" in window && "wakeLock" in navigator) {
      setIsWakeLockSupported(true)
      console.log("[Wake Lock] Wake Lock API is supported")
    } else {
      console.log("[Wake Lock] Wake Lock API is not supported")
    }
  }, [])

  // Request wake lock when timer is active
  const requestWakeLock = async () => {
    if (!isWakeLockSupported || wakeLockRef.current) {
      return
    }

    try {
      wakeLockRef.current = await navigator.wakeLock.request("screen")
      setIsWakeLockActive(true)
      onWakeLockChange?.(true)
      
      console.log("[Wake Lock] Screen wake lock acquired")

      // Listen for wake lock release
      wakeLockRef.current.addEventListener("release", () => {
        console.log("[Wake Lock] Screen wake lock was released")
        setIsWakeLockActive(false)
        onWakeLockChange?.(false)
        wakeLockRef.current = null
      })
    } catch (error) {
      console.error("[Wake Lock] Failed to acquire screen wake lock:", error)
    }
  }

  // Release wake lock when timer is stopped
  const releaseWakeLock = async () => {
    if (wakeLockRef.current) {
      try {
        await wakeLockRef.current.release()
        wakeLockRef.current = null
        setIsWakeLockActive(false)
        onWakeLockChange?.(false)
        console.log("[Wake Lock] Screen wake lock released manually")
      } catch (error) {
        console.error("[Wake Lock] Failed to release screen wake lock:", error)
      }
    }
  }

  // Handle visibility change (re-acquire wake lock when page becomes visible)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible" && isActive && !wakeLockRef.current) {
        console.log("[Wake Lock] Page became visible, re-acquiring wake lock")
        requestWakeLock()
      }
    }

    document.addEventListener("visibilitychange", handleVisibilityChange)
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange)
    }
  }, [isActive])

  // Manage wake lock based on isActive prop
  useEffect(() => {
    if (isActive) {
      requestWakeLock()
    } else {
      releaseWakeLock()
    }
  }, [isActive])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (wakeLockRef.current) {
        wakeLockRef.current.release()
      }
    }
  }, [])

  // This component doesn't render anything visible
  return null
}

// Hook for managing notification permissions
export function useNotificationPermission() {
  const [permission, setPermission] = useState<NotificationPermission>("default")
  const [isSupported, setIsSupported] = useState(false)

  useEffect(() => {
    if (typeof window !== "undefined" && "Notification" in window) {
      setIsSupported(true)
      setPermission(Notification.permission)
      console.log("[Notifications] Notification API is supported, current permission:", Notification.permission)
    } else {
      console.log("[Notifications] Notification API is not supported")
    }
  }, [])

  const requestPermission = async (): Promise<NotificationPermission> => {
    if (!isSupported) {
      console.warn("[Notifications] Notification API not supported")
      return "denied"
    }

    try {
      const result = await Notification.requestPermission()
      setPermission(result)
      console.log("[Notifications] Permission request result:", result)
      return result
    } catch (error) {
      console.error("[Notifications] Failed to request notification permission:", error)
      return "denied"
    }
  }

  const sendNotificationToServiceWorker = (
    title: string,
    body: string,
    icon?: string,
    tag?: string,
    requireInteraction: boolean = true
  ) => {
    if (permission !== "granted") {
      console.warn("[Notifications] Permission not granted, cannot send notification")
      return
    }

    if ("serviceWorker" in navigator) {
      navigator.serviceWorker.ready.then((registration) => {
        if (registration.active) {
          registration.active.postMessage({
            type: "TIMER_FINISHED",
            title,
            body,
            icon,
            tag,
            requireInteraction
          })
        }
      })
    }
  }

  const scheduleNotificationInServiceWorker = (
    delay: number,
    title: string,
    body: string,
    icon?: string,
    tag?: string,
    requireInteraction: boolean = true
  ) => {
    if (permission !== "granted") {
      console.warn("[Notifications] Permission not granted, cannot schedule notification")
      return
    }

    if ("serviceWorker" in navigator) {
      navigator.serviceWorker.ready.then((registration) => {
        if (registration.active) {
          registration.active.postMessage({
            type: "SCHEDULE_TIMER_NOTIFICATION",
            delay,
            title,
            body,
            icon,
            tag,
            requireInteraction
          })
        }
      })
    }
  }

  return {
    permission,
    isSupported,
    requestPermission,
    sendNotificationToServiceWorker,
    scheduleNotificationInServiceWorker
  }
}

// Hook for enhanced audio context management
export function useEnhancedAudioContext() {
  const audioContextRef = useRef<AudioContext | null>(null)
  const [isAudioContextActive, setIsAudioContextActive] = useState(false)

  const initializeAudioContext = async () => {
    if (typeof window === "undefined" || audioContextRef.current) return

    try {
      const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext
      if (AudioContextClass) {
        audioContextRef.current = new AudioContextClass()
        
        // Resume audio context if it's suspended
        if (audioContextRef.current.state === "suspended") {
          await audioContextRef.current.resume()
        }
        
        setIsAudioContextActive(audioContextRef.current.state === "running")
        console.log("[Audio] AudioContext initialized, state:", audioContextRef.current.state)
      }
    } catch (error) {
      console.error("[Audio] Failed to initialize AudioContext:", error)
    }
  }

  const resumeAudioContext = async () => {
    if (audioContextRef.current && audioContextRef.current.state === "suspended") {
      try {
        await audioContextRef.current.resume()
        setIsAudioContextActive(true)
        console.log("[Audio] AudioContext resumed")
      } catch (error) {
        console.error("[Audio] Failed to resume AudioContext:", error)
      }
    }
  }

  // Handle user interaction to resume audio context
  useEffect(() => {
    const handleUserInteraction = () => {
      resumeAudioContext()
    }

    // Add event listeners for user interactions
    document.addEventListener("click", handleUserInteraction, { once: true })
    document.addEventListener("touchstart", handleUserInteraction, { once: true })
    document.addEventListener("keydown", handleUserInteraction, { once: true })

    return () => {
      document.removeEventListener("click", handleUserInteraction)
      document.removeEventListener("touchstart", handleUserInteraction)
      document.removeEventListener("keydown", handleUserInteraction)
    }
  }, [])

  return {
    audioContext: audioContextRef.current,
    isAudioContextActive,
    initializeAudioContext,
    resumeAudioContext
  }
}
