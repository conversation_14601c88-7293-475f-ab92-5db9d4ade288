let intervalId = null;
let endTime = 0;
let lastSentSecond = -1; // Track the last second we sent to avoid duplicate messages
let isRunning = false;
let lastTickTime = 0;

// Enhanced timer with better background handling
self.onmessage = function(event) {
  const { command, value } = event.data;

  if (command === 'start') {
    endTime = value; // value is the target end time in milliseconds
    lastSentSecond = -1; // Reset for new countdown
    isRunning = true;
    lastTickTime = Date.now();

    // Clear any existing interval before starting a new one
    if (intervalId) {
      clearInterval(intervalId);
    }

    // Use a more frequent interval for better accuracy, especially when screen is locked
    intervalId = setInterval(() => {
      if (!isRunning) return;

      const now = Date.now();
      const timeLeftMs = Math.max(0, endTime - now);
      const currentSecond = Math.round(timeLeftMs / 1000); // Calculate current second

      // Always send tick when timer reaches zero, regardless of last sent second
      const shouldSendTick = (currentSecond !== lastSentSecond) || (timeLeftMs <= 0);

      if (shouldSendTick) {
        self.postMessage({
          type: 'tick',
          timeLeft: timeLeftMs,
          timestamp: now,
          isFinished: timeLeftMs <= 0
        });
        lastSentSecond = currentSecond;
        lastTickTime = now;
      }

      if (timeLeftMs <= 0) {
        // Timer finished
        clearInterval(intervalId);
        intervalId = null;
        isRunning = false;
        lastSentSecond = -1;

        // Send a final completion message
        self.postMessage({
          type: 'finished',
          timeLeft: 0,
          timestamp: now
        });

        // Send immediate notification request
        self.postMessage({
          type: 'requestNotification',
          notificationType: 'timerComplete',
          timestamp: now,
          endTime: endTime,
          actualEndTime: now
        });

        console.log('[Countdown Worker] Timer completed, notification requested');
      }
    }, 50); // Check every 50ms for better accuracy and responsiveness

    console.log('[Countdown Worker] Timer started, end time:', new Date(endTime));
  } else if (command === 'stop') {
    if (intervalId) {
      clearInterval(intervalId);
      intervalId = null;
      isRunning = false;
      lastSentSecond = -1;
      console.log('[Countdown Worker] Timer stopped');
    }
    endTime = 0; // Reset end time
  } else if (command === 'ping') {
    // Respond to ping to check if worker is alive
    self.postMessage({
      type: 'pong',
      isRunning: isRunning,
      timeLeft: isRunning ? Math.max(0, endTime - Date.now()) : 0,
      timestamp: Date.now()
    });
  }
};

// Handle potential worker suspension/resumption
let lastHeartbeat = Date.now();
const heartbeatInterval = setInterval(() => {
  const now = Date.now();
  const timeSinceLastHeartbeat = now - lastHeartbeat;

  // If more than 2 seconds have passed since last heartbeat, we might have been suspended
  if (timeSinceLastHeartbeat > 2000 && isRunning) {
    console.log('[Countdown Worker] Potential suspension detected, recalculating timer');

    // Recalculate and send current state
    const timeLeftMs = Math.max(0, endTime - now);
    const currentSecond = Math.round(timeLeftMs / 1000);

    if (timeLeftMs > 0) {
      self.postMessage({
        type: 'tick',
        timeLeft: timeLeftMs,
        timestamp: now,
        resumed: true
      });
      lastSentSecond = currentSecond;
    } else if (isRunning) {
      // Timer should have finished while suspended
      clearInterval(intervalId);
      intervalId = null;
      isRunning = false;

      self.postMessage({
        type: 'finished',
        timeLeft: 0,
        timestamp: now,
        resumed: true
      });

      // Send immediate notification request for suspended completion
      self.postMessage({
        type: 'requestNotification',
        notificationType: 'timerComplete',
        timestamp: now,
        endTime: endTime,
        actualEndTime: now,
        resumed: true
      });

      console.log('[Countdown Worker] Timer completed while suspended, notification requested');
    }
  }

  lastHeartbeat = now;
}, 1000); // Check every second
