import { NotificationTest } from "@/components/notification-test"
import { getDictionary } from "@/lib/dictionaries"

interface PageProps {
  params: {
    lang: string
  }
}

export default async function TestNotificationsPage({ params }: PageProps) {
  const dict = await getDictionary(params.lang)
  
  return (
    <div className="container mx-auto py-8">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold">
          {dict.notificationTest?.title || "Test des Notifications et Sons"}
        </h1>
        <p className="text-muted-foreground mt-2">
          {dict.notificationTest?.description || "Page de test pour déboguer les problèmes de notification et de son"}
        </p>
      </div>
      
      <NotificationTest />
      
      <div className="mt-8 text-center">
        <p className="text-sm text-muted-foreground">
          {dict.notificationTest?.devNote || "Cette page est destinée aux tests de développement uniquement"}
        </p>
      </div>
    </div>
  )
}

export async function generateMetadata({ params }: PageProps) {
  const dict = await getDictionary(params.lang)
  
  return {
    title: dict.notificationTest?.metaTitle || "Test Notifications - Timer Kit",
    description: dict.notificationTest?.metaDescription || "Page de test pour les notifications et sons de Timer Kit",
    robots: "noindex, nofollow" // Don't index test pages
  }
}
