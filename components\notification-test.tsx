"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { useNotificationPermission } from "@/components/wake-lock-manager"
import { useTimerSound } from "@/components/enhanced-sound-manager"

export function NotificationTest() {
  const [testResults, setTestResults] = useState<string[]>([])
  const { 
    permission, 
    requestPermission, 
    sendNotificationToServiceWorker,
    sendImmediateNotification 
  } = useNotificationPermission()
  const { playTimerCompletionSound } = useTimerSound()

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const testNotificationPermission = async () => {
    addResult(`Current permission: ${permission}`)
    if (permission !== "granted") {
      const result = await requestPermission()
      addResult(`Permission request result: ${result}`)
    }
  }

  const testServiceWorkerNotification = () => {
    addResult("Testing service worker notification...")
    sendNotificationToServiceWorker(
      "Test Service Worker",
      "This is a test notification via service worker",
      "/icons/icon-192x192.png",
      "test-sw",
      true
    )
    addResult("Service worker notification sent")
  }

  const testImmediateNotification = async () => {
    addResult("Testing immediate notification...")
    try {
      const success = await sendImmediateNotification(
        "Test Immediate",
        "This is an immediate test notification",
        "/icons/icon-192x192.png",
        "test-immediate",
        true
      )
      addResult(`Immediate notification result: ${success ? "SUCCESS" : "FAILED"}`)
    } catch (error) {
      addResult(`Immediate notification error: ${error}`)
    }
  }

  const testEnhancedSound = () => {
    addResult("Testing enhanced sound...")
    try {
      playTimerCompletionSound("Test Timer")
      addResult("Enhanced sound triggered")
    } catch (error) {
      addResult(`Enhanced sound error: ${error}`)
    }
  }

  const testDirectNotification = () => {
    addResult("Testing direct notification API...")
    try {
      new Notification("Test Direct", {
        body: "This is a direct notification test",
        icon: "/icons/icon-192x192.png",
        tag: "test-direct",
        requireInteraction: true
      })
      addResult("Direct notification sent")
    } catch (error) {
      addResult(`Direct notification error: ${error}`)
    }
  }

  const testScheduledNotification = () => {
    addResult("Testing scheduled notification (5 seconds)...")
    setTimeout(() => {
      sendNotificationToServiceWorker(
        "Scheduled Test",
        "This notification was scheduled 5 seconds ago",
        "/icons/icon-192x192.png",
        "test-scheduled",
        true
      )
      addResult("Scheduled notification sent")
    }, 5000)
  }

  const clearResults = () => {
    setTestResults([])
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>🔔 Notification & Sound Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-2">
          <Button onClick={testNotificationPermission} variant="outline">
            Check Permission
          </Button>
          <Button onClick={testServiceWorkerNotification} variant="outline">
            Test Service Worker
          </Button>
          <Button onClick={testImmediateNotification} variant="outline">
            Test Immediate
          </Button>
          <Button onClick={testEnhancedSound} variant="outline">
            Test Enhanced Sound
          </Button>
          <Button onClick={testDirectNotification} variant="outline">
            Test Direct API
          </Button>
          <Button onClick={testScheduledNotification} variant="outline">
            Test Scheduled (5s)
          </Button>
        </div>
        
        <div className="flex gap-2">
          <Button onClick={clearResults} variant="destructive" size="sm">
            Clear Results
          </Button>
          <span className="text-sm text-muted-foreground">
            Permission: {permission} | Tests: {testResults.length}
          </span>
        </div>

        <div className="bg-muted p-4 rounded-lg max-h-64 overflow-y-auto">
          <h4 className="font-semibold mb-2">Test Results:</h4>
          {testResults.length === 0 ? (
            <p className="text-muted-foreground text-sm">No tests run yet</p>
          ) : (
            <div className="space-y-1">
              {testResults.map((result, index) => (
                <div key={index} className="text-sm font-mono">
                  {result}
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="text-xs text-muted-foreground space-y-1">
          <p><strong>Instructions:</strong></p>
          <p>1. First click "Check Permission" to ensure notifications are allowed</p>
          <p>2. Test different notification methods to see which works best</p>
          <p>3. Try locking your screen and testing again</p>
          <p>4. Check browser console for detailed logs</p>
        </div>
      </CardContent>
    </Card>
  )
}
