"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { useNotificationPermission } from "@/components/wake-lock-manager"
import { useTimerSound } from "@/components/enhanced-sound-manager"

export function NotificationTest() {
  const [testResults, setTestResults] = useState<string[]>([])
  const { 
    permission, 
    requestPermission, 
    sendNotificationToServiceWorker,
    sendImmediateNotification 
  } = useNotificationPermission()
  const { playTimerCompletionSound } = useTimerSound()

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const testNotificationPermission = async () => {
    addResult(`Current permission: ${permission}`)
    if (permission !== "granted") {
      const result = await requestPermission()
      addResult(`Permission request result: ${result}`)
    }
  }

  const testServiceWorkerNotification = () => {
    addResult("Testing service worker notification...")
    sendNotificationToServiceWorker(
      "Test Service Worker",
      "This is a test notification via service worker",
      "/icons/icon-192x192.png",
      "test-sw",
      true
    )
    addResult("Service worker notification sent")
  }

  const testImmediateNotification = async () => {
    addResult("Testing immediate notification...")
    try {
      const success = await sendImmediateNotification(
        "Test Immediate",
        "This is an immediate test notification",
        "/icons/icon-192x192.png",
        "test-immediate",
        true
      )
      addResult(`Immediate notification result: ${success ? "SUCCESS" : "FAILED"}`)
    } catch (error) {
      addResult(`Immediate notification error: ${error}`)
    }
  }

  const testEnhancedSound = () => {
    addResult("Testing enhanced sound...")
    try {
      playTimerCompletionSound("Test Timer")
      addResult("Enhanced sound triggered")
    } catch (error) {
      addResult(`Enhanced sound error: ${error}`)
    }
  }

  const testDirectNotification = () => {
    addResult("Testing direct notification API...")
    try {
      const isDesktop = !/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)

      const notificationOptions: NotificationOptions & {
        vibrate?: number[];
        renotify?: boolean;
        image?: string;
        actions?: Array<{action: string, title: string}>
      } = {
        body: "This is a direct notification test",
        icon: "/icons/icon-192x192.png",
        tag: "test-direct",
        requireInteraction: true,
        silent: false,
        renotify: true
      }

      // Add desktop-specific features
      if (isDesktop) {
        notificationOptions.image = "/icons/icon-512x512.png"
        notificationOptions.actions = [
          { action: 'dismiss', title: 'Fermer' },
          { action: 'open', title: 'Ouvrir' }
        ]
        addResult("Added desktop-specific features")
      } else {
        notificationOptions.vibrate = [300, 100, 300]
        addResult("Added mobile-specific features")
      }

      new Notification("Test Direct", notificationOptions)
      addResult(`Direct notification sent (${isDesktop ? 'desktop' : 'mobile'} mode)`)
    } catch (error) {
      addResult(`Direct notification error: ${error}`)
    }
  }

  const testScheduledNotification = () => {
    addResult("Testing scheduled notification (5 seconds)...")
    setTimeout(() => {
      sendNotificationToServiceWorker(
        "Scheduled Test",
        "This notification was scheduled 5 seconds ago",
        "/icons/icon-192x192.png",
        "test-scheduled",
        true
      )
      addResult("Scheduled notification sent")
    }, 5000)
  }

  const testDesktopNotification = () => {
    addResult("Testing desktop-optimized notification...")
    try {
      const isDesktop = !/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)

      if (!isDesktop) {
        addResult("Not on desktop - testing mobile-optimized notification instead")
      }

      // Desktop-optimized notification
      const notificationOptions: NotificationOptions & {
        image?: string;
        renotify?: boolean;
        timestamp?: number;
      } = {
        body: "This notification is optimized for desktop with rich features",
        icon: "/icons/icon-192x192.png",
        image: "/icons/icon-512x512.png",
        tag: "desktop-test",
        requireInteraction: true,
        silent: false,
        renotify: true,
        timestamp: Date.now(),
        dir: "auto",
        lang: "fr"
      }

      const notification = new Notification("🖥️ Desktop Timer Alert", notificationOptions)

      // Handle notification events
      notification.onclick = () => {
        addResult("Desktop notification clicked!")
        notification.close()
      }

      notification.onclose = () => {
        addResult("Desktop notification closed")
      }

      notification.onerror = (error) => {
        addResult(`Desktop notification error: ${error}`)
      }

      addResult(`Desktop notification sent (platform: ${isDesktop ? 'desktop' : 'mobile'})`)
    } catch (error) {
      addResult(`Desktop notification error: ${error}`)
    }
  }

  const testBrowserInfo = () => {
    addResult("=== Browser & Platform Info ===")
    addResult(`User Agent: ${navigator.userAgent}`)
    addResult(`Platform: ${navigator.platform}`)
    addResult(`Language: ${navigator.language}`)
    addResult(`Online: ${navigator.onLine}`)
    addResult(`Notification support: ${"Notification" in window}`)
    addResult(`Service Worker support: ${"serviceWorker" in navigator}`)
    addResult(`PWA mode: ${window.matchMedia('(display-mode: standalone)').matches}`)

    const isDesktop = !/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    addResult(`Detected platform: ${isDesktop ? 'Desktop' : 'Mobile'}`)

    if ("Notification" in window) {
      addResult(`Notification permission: ${Notification.permission}`)
      addResult(`Max actions supported: ${(Notification as any).maxActions || 'Unknown'}`)
    }
  }

  const runDesktopDiagnostic = async () => {
    addResult("=== DIAGNOSTIC DESKTOP NOTIFICATIONS ===")

    // 1. Check basic support
    if (!("Notification" in window)) {
      addResult("❌ ERREUR: Notifications non supportées par ce navigateur")
      return
    }
    addResult("✅ Support des notifications: OK")

    // 2. Check permission
    const permission = Notification.permission
    addResult(`Permission actuelle: ${permission}`)

    if (permission === "denied") {
      addResult("❌ ERREUR: Notifications refusées - Réactivez dans les paramètres du navigateur")
      addResult("Chrome: chrome://settings/content/notifications")
      addResult("Firefox: about:preferences#privacy")
      addResult("Edge: edge://settings/content/notifications")
      return
    }

    if (permission === "default") {
      addResult("⚠️ Permission non demandée - Cliquez 'Check Permission' d'abord")
      return
    }

    addResult("✅ Permission accordée: OK")

    // 3. Test simple notification
    try {
      const testNotif = new Notification("Test Diagnostic", {
        body: "Si vous voyez ceci, les notifications fonctionnent!",
        icon: "/icons/icon-192x192.png",
        tag: "diagnostic-test"
      })

      addResult("✅ Notification de test créée")

      // Auto-close after 3 seconds
      setTimeout(() => {
        testNotif.close()
        addResult("✅ Notification fermée automatiquement")
      }, 3000)

    } catch (error) {
      addResult(`❌ ERREUR lors de la création: ${error}`)
    }

    // 4. Check Service Worker
    if ("serviceWorker" in navigator) {
      try {
        const registration = await navigator.serviceWorker.ready
        if (registration.active) {
          addResult("✅ Service Worker actif: OK")
        } else {
          addResult("⚠️ Service Worker non actif")
        }
      } catch (error) {
        addResult(`❌ Erreur Service Worker: ${error}`)
      }
    } else {
      addResult("❌ Service Worker non supporté")
    }

    // 5. Platform detection
    const isDesktop = !/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    addResult(`Plateforme détectée: ${isDesktop ? '🖥️ Desktop' : '📱 Mobile'}`)

    // 6. Browser detection
    const userAgent = navigator.userAgent
    let browser = "Inconnu"
    if (userAgent.includes("Chrome")) browser = "Chrome"
    else if (userAgent.includes("Firefox")) browser = "Firefox"
    else if (userAgent.includes("Safari")) browser = "Safari"
    else if (userAgent.includes("Edge")) browser = "Edge"

    addResult(`Navigateur: ${browser}`)

    addResult("=== FIN DU DIAGNOSTIC ===")
  }

  const clearResults = () => {
    setTestResults([])
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>🔔 Notification & Sound Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-2">
          <Button onClick={testNotificationPermission} variant="outline">
            Check Permission
          </Button>
          <Button onClick={testServiceWorkerNotification} variant="outline">
            Test Service Worker
          </Button>
          <Button onClick={testImmediateNotification} variant="outline">
            Test Immediate
          </Button>
          <Button onClick={testEnhancedSound} variant="outline">
            Test Enhanced Sound
          </Button>
          <Button onClick={testDirectNotification} variant="outline">
            Test Direct API
          </Button>
          <Button onClick={testScheduledNotification} variant="outline">
            Test Scheduled (5s)
          </Button>
          <Button onClick={testDesktopNotification} variant="outline">
            🖥️ Test Desktop
          </Button>
          <Button onClick={testBrowserInfo} variant="outline">
            📊 Browser Info
          </Button>
        </div>

        <div className="flex gap-2 justify-center">
          <Button onClick={runDesktopDiagnostic} variant="default" className="bg-blue-600 hover:bg-blue-700">
            🔍 Diagnostic Desktop
          </Button>
        </div>
        
        <div className="flex gap-2">
          <Button onClick={clearResults} variant="destructive" size="sm">
            Clear Results
          </Button>
          <span className="text-sm text-muted-foreground">
            Permission: {permission} | Tests: {testResults.length}
          </span>
        </div>

        <div className="bg-muted p-4 rounded-lg max-h-64 overflow-y-auto">
          <h4 className="font-semibold mb-2">Test Results:</h4>
          {testResults.length === 0 ? (
            <p className="text-muted-foreground text-sm">No tests run yet</p>
          ) : (
            <div className="space-y-1">
              {testResults.map((result, index) => (
                <div key={index} className="text-sm font-mono">
                  {result}
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="text-xs text-muted-foreground space-y-1">
          <p><strong>Instructions pour Desktop:</strong></p>
          <p>1. Cliquez "Check Permission" pour autoriser les notifications</p>
          <p>2. Cliquez "🖥️ Test Desktop" pour tester les notifications desktop optimisées</p>
          <p>3. Cliquez "📊 Browser Info" pour voir les détails de votre navigateur</p>
          <p>4. Les notifications desktop apparaissent généralement en haut à droite</p>
          <p>5. Vérifiez les paramètres de notification de votre navigateur si elles n'apparaissent pas</p>
          <p>6. Chrome: Paramètres → Confidentialité et sécurité → Paramètres du site → Notifications</p>
          <p>7. Firefox: Paramètres → Vie privée et sécurité → Permissions → Notifications</p>
          <p>8. Edge: Paramètres → Cookies et autorisations de site → Notifications</p>
        </div>
      </CardContent>
    </Card>
  )
}
