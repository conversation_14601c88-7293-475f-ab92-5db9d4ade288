"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Bell, X } from "lucide-react"
import { useLanguage } from "@/components/language-provider"
import { getTranslations } from "@/lib/i18n/translations"

export function NotificationPermissionBanner() {
  const [showBanner, setShowBanner] = useState(false)
  const [permission, setPermission] = useState<NotificationPermission>("default")
  const { language } = useLanguage()
  const t = getTranslations(language || "en")

  useEffect(() => {
    // Check if notifications are supported and permission status
    if (typeof window !== "undefined" && "Notification" in window) {
      const currentPermission = Notification.permission
      setPermission(currentPermission)
      
      // Show banner if permission is default (not asked yet)
      // Show on all platforms (desktop, mobile, PWA) for better notification support
      const isPWA = window.matchMedia('(display-mode: standalone)').matches ||
                   (window.navigator as any).standalone === true
      const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
      const isDesktop = !isMobile

      // Show banner on all platforms to ensure notifications work everywhere
      if (currentPermission === "default") {
        console.log(`[Notification Banner] Showing on ${isDesktop ? 'desktop' : 'mobile'} ${isPWA ? '(PWA)' : '(browser)'}`)

        // Show banner after a short delay to not overwhelm the user
        // Shorter delay on desktop as notifications are more expected
        const delay = isDesktop ? 2000 : 3000
        const timer = setTimeout(() => {
          setShowBanner(true)
        }, delay)

        return () => clearTimeout(timer)
      }
    }
  }, [])

  const requestPermission = async () => {
    if ("Notification" in window) {
      try {
        const result = await Notification.requestPermission()
        setPermission(result)
        setShowBanner(false)
        
        if (result === "granted") {
          // Show a test notification
          new Notification(t.notificationTestTitle || "Notifications activées", {
            body: t.notificationTestBody || "Vous recevrez des alertes quand vos minuteurs se terminent.",
            icon: "/icons/icon-192x192.png",
            silent: true,
            requireInteraction: false
          })
        }
      } catch (error) {
        console.error("Error requesting notification permission:", error)
        setShowBanner(false)
      }
    }
  }

  const dismissBanner = () => {
    setShowBanner(false)
    // Remember that user dismissed the banner
    if (typeof window !== "undefined") {
      localStorage.setItem("notification-banner-dismissed", "true")
    }
  }

  // Don't show if permission already granted/denied or banner was dismissed
  useEffect(() => {
    if (typeof window !== "undefined") {
      const wasDismissed = localStorage.getItem("notification-banner-dismissed")
      if (wasDismissed || permission !== "default") {
        setShowBanner(false)
      }
    }
  }, [permission])

  if (!showBanner) {
    return null
  }

  return (
    <Card className="fixed bottom-4 left-4 right-4 z-50 border-orange-200 bg-orange-50 dark:bg-orange-950 dark:border-orange-800 max-w-md mx-auto">
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <Bell className="h-5 w-5 text-orange-600 dark:text-orange-400 mt-0.5 flex-shrink-0" />
          <div className="flex-1 min-w-0">
            <h3 className="font-medium text-orange-900 dark:text-orange-100 mb-1">
              {t.enableNotificationsTitle || "Activer les notifications"}
            </h3>
            <p className="text-sm text-orange-700 dark:text-orange-300 mb-3">
              {t.enableNotificationsDesc || "Recevez des alertes quand vos minuteurs se terminent, même si l'écran est verrouillé."}
            </p>
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={requestPermission}
                className="bg-orange-600 hover:bg-orange-700 text-white"
              >
                {t.enable || "Activer"}
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={dismissBanner}
                className="text-orange-700 hover:text-orange-900 dark:text-orange-300 dark:hover:text-orange-100"
              >
                {t.later || "Plus tard"}
              </Button>
            </div>
          </div>
          <Button
            size="sm"
            variant="ghost"
            onClick={dismissBanner}
            className="p-1 h-auto text-orange-700 hover:text-orange-900 dark:text-orange-300 dark:hover:text-orange-100"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">{t.close || "Fermer"}</span>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

// Hook to check if we should show notification-related UI
export function useNotificationStatus() {
  const [permission, setPermission] = useState<NotificationPermission>("default")
  const [isSupported, setIsSupported] = useState(false)

  useEffect(() => {
    if (typeof window !== "undefined" && "Notification" in window) {
      setIsSupported(true)
      setPermission(Notification.permission)
      
      // Listen for permission changes
      const checkPermission = () => {
        setPermission(Notification.permission)
      }
      
      // Check permission periodically (some browsers don't fire events)
      const interval = setInterval(checkPermission, 1000)
      
      return () => clearInterval(interval)
    }
  }, [])

  return {
    permission,
    isSupported,
    isGranted: permission === "granted",
    isDenied: permission === "denied",
    isDefault: permission === "default"
  }
}
